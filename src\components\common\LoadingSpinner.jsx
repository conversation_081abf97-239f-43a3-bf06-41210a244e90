import React from 'react';

/**
 * Loading spinner component for indicating loading states
 * @param {Object} props - Component props
 * @param {string} props.size - Size of the spinner (sm, md, lg)
 * @param {string} props.color - Color of the spinner (default: orange)
 */
export default function LoadingSpinner({ size = 'md', color = 'orange' }) {
  // Determine size class
  const sizeClass = {
    sm: 'h-6 w-6 border-2',
    md: 'h-10 w-10 border-3',
    lg: 'h-16 w-16 border-4'
  }[size] || 'h-10 w-10 border-3';

  // Determine color class
  const colorClass = {
    orange: 'border-orange-500',
    blue: 'border-blue-500',
    green: 'border-green-500',
    red: 'border-red-500',
    gray: 'border-gray-500'
  }[color] || 'border-orange-500';

  return (
    <div className="flex justify-center items-center p-4">
      <div className={`animate-spin rounded-full ${sizeClass} border-t-transparent ${colorClass}`}></div>
    </div>
  );
}
