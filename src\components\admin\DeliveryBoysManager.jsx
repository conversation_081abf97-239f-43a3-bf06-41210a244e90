import { useState, useEffect, useCallback } from "react";
import DataTable from "./DataTable";
import LoadingSpinner from '../common/LoadingSpinner';
import ErrorAlert from "../common/ErrorAlert";
import SuccessAlert from "../common/SuccessAlert";

export default function DeliveryBoysManager() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [deliveryBoys, setDeliveryBoys] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [locations, setLocations] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState("");
  const [selectedLocations, setSelectedLocations] = useState([]);
  const [selectedDeliveryBoy, setSelectedDeliveryBoy] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showAssignForm, setShowAssignForm] = useState(false);
  const [customerSearch, setCustomerSearch] = useState("");
  const [searchTimeout, setSearchTimeout] = useState(null);

  // Function to search for customers
  const searchCustomers = useCallback(async (searchTerm) => {
    if (!searchTerm || searchTerm.trim().length < 2) {
      setCustomers([]);
      return;
    }

    try {
      setIsSearching(true);
      setError(null);

      // Search for customers with the given search term
      const customersResponse = await window.ApiClient.getAdminCustomers(1, 10, searchTerm);
      if (customersResponse.success) {
        // Filter out customers who are already delivery boys
        const filteredCustomers = customersResponse.customers.filter(
          (customer) => !deliveryBoys.some((boy) => boy.id === customer.id)
        );
        setCustomers(filteredCustomers || []);
      }
    } catch (err) {
      console.error("Error searching customers:", err);
      setError("Failed to search customers. Please try again.");
    } finally {
      setIsSearching(false);
    }
  }, [deliveryBoys]);

  // Handle customer search input with debounce
  const handleCustomerSearchChange = (e) => {
    const value = e.target.value;
    setCustomerSearch(value);

    // Clear any existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set a new timeout to delay the search
    const timeoutId = setTimeout(() => {
      searchCustomers(value);
    }, 500); // 500ms debounce

    setSearchTimeout(timeoutId);
  };

  // Fetch delivery boys and locations on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch delivery boys with their locations
        // Pass a dummy locationId parameter to ensure we get the locations for each delivery boy
        const deliveryBoysResponse =
          await window.ApiClient.getAdminDeliveryBoys('all');
        if (deliveryBoysResponse.success) {
          setDeliveryBoys(deliveryBoysResponse.deliveryBoys || []);
        }

        // Fetch locations
        const locationsResponse = await window.ApiClient.getAdminOrderLocations();
        if (locationsResponse.success) {
          setLocations(locationsResponse.locations || []);
        }
      } catch (err) {
        console.error("Error fetching data:", err);
        setError("Failed to load data. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();

    // Cleanup function to clear any pending timeouts
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, []);

  // Handle adding a new delivery boy
  const handleAddDeliveryBoy = async (e) => {
    e.preventDefault();

    if (!selectedCustomer) {
      setError("Please select a customer");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await window.ApiClient.addDeliveryBoy(
        parseInt(selectedCustomer),
        selectedLocations.map((loc) => parseInt(loc))
      );

      if (response.success) {
        setSuccess("Delivery boy added successfully");
        setShowAddForm(false);
        setSelectedCustomer("");
        setSelectedLocations([]);

        // Refresh delivery boys list with their locations
        const deliveryBoysResponse =
          await window.ApiClient.getAdminDeliveryBoys('all');
        if (deliveryBoysResponse.success) {
          setDeliveryBoys(deliveryBoysResponse.deliveryBoys || []);
        }
      } else {
        setError(response.error || "Failed to add delivery boy");
      }
    } catch (err) {
      console.error("Error adding delivery boy:", err);
      setError("Failed to add delivery boy. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle assigning a location to a delivery boy
  const handleAssignLocation = async (e) => {
    e.preventDefault();

    if (!selectedDeliveryBoy || selectedLocations.length === 0) {
      setError("Please select a delivery boy and at least one location");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Assign each selected location to the delivery boy
      const promises = selectedLocations.map((locationId) =>
        window.ApiClient.updateDeliveryBoy(
          selectedDeliveryBoy.id,
          "add_location",
          parseInt(locationId)
        )
      );

      await Promise.all(promises);

      setSuccess("Locations assigned successfully");
      setShowAssignForm(false);
      setSelectedDeliveryBoy(null);
      setSelectedLocations([]);

      // Refresh delivery boys list with their locations
      const deliveryBoysResponse =
        await window.ApiClient.getAdminDeliveryBoys('all');
      if (deliveryBoysResponse.success) {
        setDeliveryBoys(deliveryBoysResponse.deliveryBoys || []);
      }
    } catch (err) {
      console.error("Error assigning location:", err);
      setError("Failed to assign location. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle removing a delivery boy
  const handleRemoveDeliveryBoy = async (deliveryBoyId) => {
    if (!confirm("Are you sure you want to remove this delivery boy?")) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await window.ApiClient.updateDeliveryBoy(
        deliveryBoyId,
        "remove_role"
      );

      if (response.success) {
        setSuccess("Delivery boy removed successfully");

        // Remove from state
        setDeliveryBoys((prevBoys) =>
          prevBoys.filter((boy) => boy.id !== deliveryBoyId)
        );
      } else {
        setError(response.error || "Failed to remove delivery boy");
      }
    } catch (err) {
      console.error("Error removing delivery boy:", err);
      setError("Failed to remove delivery boy. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle removing a location from a delivery boy
  const handleRemoveLocation = async (deliveryBoyId, locationId) => {
    if (!confirm("Are you sure you want to remove this location assignment?")) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await window.ApiClient.updateDeliveryBoy(
        deliveryBoyId,
        "remove_location",
        locationId
      );

      if (response.success) {
        setSuccess("Location removed successfully");

        // Refresh delivery boys list with their locations
        const deliveryBoysResponse =
          await window.ApiClient.getAdminDeliveryBoys('all');
        if (deliveryBoysResponse.success) {
          setDeliveryBoys(deliveryBoysResponse.deliveryBoys || []);
        }
      } else {
        setError(response.error || "Failed to remove location");
      }
    } catch (err) {
      console.error("Error removing location:", err);
      setError("Failed to remove location. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Table columns for delivery boys
  const columns = [
    {
      header: "ID",
      accessor: "id",
      cell: ({ row: { original } }) => <span className="font-medium">{original.id}</span>,
    },
    {
      header: "Name",
      accessor: "name",
      cell: ({ row: { original } }) => <span className="font-medium">{original.name}</span>,
    },
    {
      header: "Phone",
      accessor: "phone_number",
      cell: ({ row: { original } }) => <span>{original.phone_number}</span>,
    },
    {
      header: "Email",
      accessor: "email",
      cell: ({ row: { original } }) => <span>{original.email || "-"}</span>,
    },
    {
      header: "Status",
      accessor: "is_verified",
      cell: ({ row: { original } }) => (
        <div className="flex items-center">
          {original.is_verified === 1 ? (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              <span className="material-icons-round text-xs mr-1">check_circle</span>
              Verified
            </span>
          ) : (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
              <span className="material-icons-round text-xs mr-1">pending</span>
              Pending
            </span>
          )}
        </div>
      ),
    },
    {
      header: "Created At",
      accessor: "created_at",
      cell: ({ row: { original } }) => {
        // Format the date to a more readable format
        const date = new Date(original.created_at);
        const formattedDate = date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
        return <span>{formattedDate}</span>;
      },
    },
    {
      header: "Assigned Locations",
      accessor: "locations",
      cell: ({ row: { original } }) => (
        <div className="flex flex-wrap gap-1">
          {original.locations && original.locations.length > 0 ? (
            original.locations.map((location) => (
              <div
                key={location.id}
                className="flex items-center bg-gray-100 rounded-full px-2 py-1 text-xs"
              >
                <span>{location.name}</span>
                <button
                  onClick={() => handleRemoveLocation(original.id, location.id)}
                  className="ml-1 text-red-500 hover:text-red-700"
                  title="Remove location"
                >
                  <span className="material-icons-round text-xs">close</span>
                </button>
              </div>
            ))
          ) : (
            <span className="text-gray-500">No locations assigned</span>
          )}
        </div>
      ),
    },

  ];

  // Note: Customers are already filtered in the searchCustomers function

  // Get available locations for a delivery boy (not already assigned)
  const getAvailableLocations = (deliveryBoy) => {
    if (!deliveryBoy || !deliveryBoy.locations) return locations;

    return locations.filter(
      (location) => !deliveryBoy.locations.some((loc) => loc.id === location.id)
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">Delivery Boys</h1>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <span className="material-icons-round mr-1">add</span>
          Add Delivery Boy
        </button>
      </div>

      {error && <ErrorAlert message={error} onClose={() => setError(null)} />}
      {success && (
        <SuccessAlert message={success} onClose={() => setSuccess(null)} />
      )}

      {isLoading && <LoadingSpinner size="lg" />}

      {/* Add Delivery Boy Form */}
      {showAddForm && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Add New Delivery Boy</h2>
            <button
              onClick={() => {
                setShowAddForm(false);
                setCustomerSearch("");
                setCustomers([]);
                setSelectedCustomer("");
              }}
              className="text-gray-500 hover:text-gray-700"
            >
              <span className="material-icons-round">close</span>
            </button>
          </div>

          <form onSubmit={handleAddDeliveryBoy}>
            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-medium mb-2">
                Search Customer
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="material-icons-round text-gray-400 text-sm">search</span>
                </div>
                <input
                  type="text"
                  value={customerSearch}
                  onChange={handleCustomerSearchChange}
                  placeholder="Search by name or phone number..."
                  className="w-full border border-gray-300 rounded-md pl-10 pr-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
                />
              </div>
              {isSearching && (
                <div className="flex items-center mt-2">
                  <div className="w-4 h-4 border-t-2 border-orange-500 rounded-full animate-spin mr-2"></div>
                  <span className="text-sm text-gray-500">Searching...</span>
                </div>
              )}
            </div>

            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-medium mb-2">
                Select Customer
              </label>
              <select
                value={selectedCustomer}
                onChange={(e) => setSelectedCustomer(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500"
                required
                disabled={customers.length === 0}
              >
                <option value="">
                  {customers.length === 0
                    ? "Search for customers first"
                    : "Select a customer"}
                </option>
                {customers.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name} ({customer.phone_number})
                  </option>
                ))}
              </select>
              {customers.length === 0 && customerSearch.length > 0 && !isSearching && (
                <p className="text-sm text-gray-500 mt-1">
                  No customers found. Try a different search term.
                </p>
              )}
            </div>

            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-medium mb-2">
                Assign Locations (Optional)
              </label>
              <select
                multiple
                value={selectedLocations}
                onChange={(e) => {
                  const values = Array.from(
                    e.target.selectedOptions,
                    (option) => option.value
                  );
                  setSelectedLocations(values);
                }}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 h-32"
              >
                {locations.map((location) => (
                  <option key={location.id} value={location.id}>
                    {location.name}
                  </option>
                ))}
              </select>
              <p className="text-xs text-gray-500 mt-1">
                Hold Ctrl/Cmd to select multiple locations
              </p>
            </div>

            <div className="flex justify-end">
              <button
                type="button"
                onClick={() => {
                  setShowAddForm(false);
                  setCustomerSearch("");
                  setCustomers([]);
                  setSelectedCustomer("");
                }}
                className="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg mr-2"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg"
                disabled={isLoading}
              >
                {isLoading ? "Adding..." : "Add Delivery Boy"}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Assign Location Form */}
      {showAssignForm && selectedDeliveryBoy && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">
              Assign Locations to {selectedDeliveryBoy.name}
            </h2>
            <button
              onClick={() => {
                setShowAssignForm(false);
                setSelectedDeliveryBoy(null);
              }}
              className="text-gray-500 hover:text-gray-700"
            >
              <span className="material-icons-round">close</span>
            </button>
          </div>

          <form onSubmit={handleAssignLocation}>
            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-medium mb-2">
                Select Locations
              </label>
              <select
                multiple
                value={selectedLocations}
                onChange={(e) => {
                  const values = Array.from(
                    e.target.selectedOptions,
                    (option) => option.value
                  );
                  setSelectedLocations(values);
                }}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 h-32"
                required
              >
                {getAvailableLocations(selectedDeliveryBoy).map((location) => (
                  <option key={location.id} value={location.id}>
                    {location.name}
                  </option>
                ))}
              </select>
              <p className="text-xs text-gray-500 mt-1">
                Hold Ctrl/Cmd to select multiple locations
              </p>
            </div>

            <div className="flex justify-end">
              <button
                type="button"
                onClick={() => {
                  setShowAssignForm(false);
                  setSelectedDeliveryBoy(null);
                }}
                className="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg mr-2"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg"
                disabled={isLoading}
              >
                {isLoading ? "Assigning..." : "Assign Locations"}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Delivery Boys Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <DataTable
          columns={columns}
          data={deliveryBoys}
          isLoading={isLoading}
          pagination={true}
          pageSize={10}
          emptyMessage="No delivery boys found"
          actions={(item) => (
            <div className="flex space-x-2 justify-end">
              <button
                onClick={() => {
                  setSelectedDeliveryBoy(item);
                  setShowAssignForm(true);
                }}
                className="text-blue-600 hover:text-blue-800"
                title="Assign Location"
              >
                <span className="material-icons-round">add_location</span>
              </button>
              <button
                onClick={() => handleRemoveDeliveryBoy(item.id)}
                className="text-red-600 hover:text-red-800"
                title="Remove Delivery Boy"
              >
                <span className="material-icons-round">delete</span>
              </button>
            </div>
          )}
        />
      </div>
    </div>
  );
}
