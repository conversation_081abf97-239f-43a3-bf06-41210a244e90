/**
 * Utility functions for managing product favorites in localStorage
 */

(function() {
  const FAVORITES_KEY = "highq_favorites";

  // For backward compatibility, check if there are favorites in the old key
  const OLD_FAVORITES_KEY = "snackswift_favorites";
  try {
    const oldFavoritesJson = localStorage.getItem(OLD_FAVORITES_KEY);
    if (oldFavoritesJson) {
      console.log('Found favorites in old storage key, migrating to new key');
      localStorage.setItem(FAVORITES_KEY, oldFavoritesJson);
      // Clear the old key to avoid duplicates
      localStorage.removeItem(OLD_FAVORITES_KEY);
    }
  } catch (error) {
    console.error("Error migrating old favorites:", error);
  }

  // Get all favorites from localStorage
  const getFavorites = () => {
    try {
      const favoritesJson = localStorage.getItem(FAVORITES_KEY);
      const favorites = favoritesJson ? JSON.parse(favoritesJson) : [];
      console.log('Retrieved favorites from localStorage:', favorites);
      return favorites;
    } catch (error) {
      console.error("Error getting favorites from localStorage:", error);
      return [];
    }
  };

  // Save favorites to localStorage
  const saveFavorites = (favorites) => {
    try {
      console.log('Saving favorites to localStorage:', favorites);
      localStorage.setItem(FAVORITES_KEY, JSON.stringify(favorites));
      // Verify the save was successful
      const savedJson = localStorage.getItem(FAVORITES_KEY);
      const savedFavorites = savedJson ? JSON.parse(savedJson) : [];
      console.log('Verified saved favorites:', savedFavorites);
    } catch (error) {
      console.error("Error saving favorites to localStorage:", error);
    }
  };

  // Check if a product is already in favorites
  const isFavorite = (productId) => {
    if (!productId) {
      console.error('Invalid product ID passed to isFavorite');
      return false;
    }

    // Convert to string to ensure consistent comparison
    const idStr = String(productId);

    const favorites = getFavorites();
    return favorites.some(item => String(item.id) === idStr);
  };

  // Toggle a product in favorites (add if not present, remove if present)
  const toggleFavorite = (product) => {
    console.log('Toggling favorite for product:', product);

    if (!product || !product.id) {
      console.error('Invalid product or missing product ID');
      return false;
    }

    const favorites = getFavorites();
    // Convert to string to ensure consistent comparison
    const productIdStr = String(product.id);
    const existingIndex = favorites.findIndex(item => String(item.id) === productIdStr);

    if (existingIndex !== -1) {
      // Remove from favorites
      console.log('Removing product from favorites, ID:', product.id);
      favorites.splice(existingIndex, 1);
      saveFavorites(favorites);
      // Dispatch an event so any UI elements can update
      window.dispatchEvent(new CustomEvent('favorites-updated'));
      return false; // Indicates it was removed
    } else {
      // Add to favorites
      console.log('Adding product to favorites, ID:', product.id);
      favorites.push({
        id: product.id,
        name: product.name,
        price: product.price,
        image_url: product.image_url || product.image,
        category_name: product.category_name,
        added_at: new Date().toISOString(),
        url_slug: product.url_slug || product.slug
      });
      saveFavorites(favorites);
      // Dispatch an event so any UI elements can update
      window.dispatchEvent(new CustomEvent('favorites-updated'));
      return true; // Indicates it was added
    }
  };

  // Add a product to favorites
  const addToFavorites = (product) => {
    if (isFavorite(product.id)) {
      return false; // Already in favorites
    }

    const favorites = getFavorites();
    favorites.push({
      id: product.id,
      name: product.name,
      price: product.price,
      image_url: product.image_url || product.image,
      category_name: product.category_name,
      added_at: new Date().toISOString(),
      url_slug: product.url_slug || product.slug
    });

    saveFavorites(favorites);
    // Dispatch an event so any UI elements can update
    window.dispatchEvent(new CustomEvent('favorites-updated'));
    return true;
  };

  // Remove a product from favorites
  const removeFavorite = (productId) => {
    const favorites = getFavorites();
    const initialLength = favorites.length;

    const updatedFavorites = favorites.filter(item => item.id !== productId);

    if (updatedFavorites.length !== initialLength) {
      saveFavorites(updatedFavorites);
      // Dispatch an event so any UI elements can update
      window.dispatchEvent(new CustomEvent('favorites-updated'));
      return true; // Successfully removed
    }

    return false; // Nothing was removed
  };

  // Clear all favorites
  const clearFavorites = () => {
    saveFavorites([]);
  };

  // Count favorites
  const countFavorites = () => {
    return getFavorites().length;
  };

  // Public API
  window.FavoritesUtils = {
    getFavorites,
    isFavorite,
    toggleFavorite,
    addToFavorites,
    removeFavorite, // Use this name consistently instead of removeFromFavorites
    clearFavorites,
    countFavorites
  };

  // Initialize on page load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      // Notify that FavoritesUtils is ready
      window.dispatchEvent(new CustomEvent('favorites-utils-ready'));
    });
  } else {
    // Notify that FavoritesUtils is ready
    window.dispatchEvent(new CustomEvent('favorites-utils-ready'));
  }

  // Also initialize on Astro page transitions
  document.addEventListener('astro:page-load', () => {
    // Notify that FavoritesUtils is ready
    window.dispatchEvent(new CustomEvent('favorites-utils-ready'));
  });
})();