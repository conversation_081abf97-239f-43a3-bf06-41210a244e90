import React from 'react';

/**
 * Success alert component for displaying success messages
 * @param {Object} props - Component props
 * @param {string} props.message - The success message to display
 * @param {Function} props.onClose - Function to call when the close button is clicked
 */
export default function SuccessAlert({ message, onClose }) {
  return (
    <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-4 rounded-md">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <span className="material-icons-round text-green-500">check_circle</span>
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-green-800">Success</h3>
          <div className="mt-1 text-sm text-green-700">
            <p>{message}</p>
          </div>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="ml-auto -mx-1.5 -my-1.5 bg-green-50 text-green-500 rounded-lg focus:ring-2 focus:ring-green-400 p-1.5 hover:bg-green-100 inline-flex h-8 w-8 items-center justify-center"
            aria-label="Close"
          >
            <span className="material-icons-round text-sm">close</span>
          </button>
        )}
      </div>
    </div>
  );
}
