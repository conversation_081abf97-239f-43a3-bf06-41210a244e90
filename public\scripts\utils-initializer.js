/**
 * Utility Initializer
 * This script ensures all utility scripts are properly loaded and initialized
 */

(function() {
  // Store references to utility initialization status
  window.UtilsInitialized = window.UtilsInitialized || {
    cart: false,
    favorites: false
  };

  // Function to load a script if it's not already loaded
  function loadScript(src, callback) {
    // Check if script is already loaded
    const existingScript = document.querySelector(`script[src="${src}"]`);
    if (existingScript) {
      if (callback) callback();
      return;
    }

    // Create and append script
    const script = document.createElement('script');
    script.src = src;
    script.async = true;
    
    if (callback) {
      script.onload = callback;
    }
    
    document.head.appendChild(script);
  }

  // Initialize cart utilities
  function initCartUtils() {
    if (window.CartUtils) {
      window.UtilsInitialized.cart = true;
      return Promise.resolve(window.CartUtils);
    }

    return new Promise((resolve) => {
      loadScript('/scripts/cart-utils.js', () => {
        // Wait a bit to ensure script is executed
        setTimeout(() => {
          if (window.CartUtils) {
            window.UtilsInitialized.cart = true;
            resolve(window.CartUtils);
          } else {
            console.error('Failed to initialize CartUtils');
            resolve(null);
          }
        }, 100);
      });
    });
  }

  // Initialize favorites utilities
  function initFavoritesUtils() {
    if (window.FavoritesUtils) {
      window.UtilsInitialized.favorites = true;
      return Promise.resolve(window.FavoritesUtils);
    }

    return new Promise((resolve) => {
      loadScript('/scripts/favorites-utils.js', () => {
        // Wait a bit to ensure script is executed
        setTimeout(() => {
          if (window.FavoritesUtils) {
            window.UtilsInitialized.favorites = true;
            resolve(window.FavoritesUtils);
          } else {
            console.error('Failed to initialize FavoritesUtils');
            resolve(null);
          }
        }, 100);
      });
    });
  }

  // Initialize all utilities
  async function initAllUtils() {
    const cartUtils = await initCartUtils();
    const favoritesUtils = await initFavoritesUtils();
    
    // Dispatch event when all utils are initialized
    if (cartUtils && favoritesUtils) {
      window.dispatchEvent(new CustomEvent('utils-initialized'));
    }
    
    return {
      cartUtils,
      favoritesUtils
    };
  }

  // Make functions available globally
  window.UtilsInitializer = {
    initCartUtils,
    initFavoritesUtils,
    initAllUtils
  };

  // Initialize on page load
  document.addEventListener('DOMContentLoaded', initAllUtils);
  
  // Initialize on Astro page transitions
  document.addEventListener('astro:page-load', initAllUtils);
})();
