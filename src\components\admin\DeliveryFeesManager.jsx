import { useState, useEffect } from 'react';
import DataTable from './DataTable';
import Modal from './Modal';
import LoadingSpinner from '../common/LoadingSpinner';
import ErrorAlert from '../common/ErrorAlert';
import SuccessAlert from '../common/SuccessAlert';

export default function DeliveryFeesManager() {
  // State for delivery fee settings
  const [settings, setSettings] = useState({
    base_fee: 2.99,
    free_delivery_threshold: 0,
    is_enabled: true
  });

  // State for location-specific fees
  const [locationFees, setLocationFees] = useState([]);
  const [locations, setLocations] = useState([]);

  // UI state
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('add'); // 'add' or 'edit'
  const [currentLocationFee, setCurrentLocationFee] = useState(null);
  const [formErrors, setFormErrors] = useState({});

  // Delete confirmation modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [locationFeeToDelete, setLocationFeeToDelete] = useState(null);

  // Form data for location fee
  const [formData, setFormData] = useState({
    locationId: '',
    fee: '',
    free_delivery_threshold: '0'
  });

  // Table columns configuration for location fees
  const columns = [
    {
      key: 'location_name',
      label: 'Location',
      render: (row) => (
        <span className="font-medium text-gray-900">{row.location_name}</span>
      )
    },
    {
      key: 'fee',
      label: 'Delivery Fee',
      render: (row) => (
        <span>₹{parseFloat(row.fee).toFixed(2)}</span>
      )
    },
    {
      key: 'free_delivery_threshold',
      label: 'Free Delivery Threshold',
      render: (row) => (
        row.free_delivery_threshold > 0
          ? <span>₹{parseFloat(row.free_delivery_threshold).toFixed(2)}</span>
          : <span className="text-gray-500">Not set</span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (row) => (
        <div className="flex items-center space-x-3">
          <button
            onClick={() => handleEditLocationFee(row)}
            className="p-1.5 bg-indigo-50 text-indigo-600 rounded-md hover:bg-indigo-100 transition-colors"
            title="Edit Location Fee"
          >
            <span className="material-icons-round text-sm">edit</span>
          </button>
          <button
            onClick={() => handleDeleteLocationFee(row)}
            className="p-1.5 bg-red-50 text-red-600 rounded-md hover:bg-red-100 transition-colors"
            title="Remove Location Fee"
          >
            <span className="material-icons-round text-sm">delete</span>
          </button>
        </div>
      )
    }
  ];

  // Fetch delivery fee settings and locations on component mount
  useEffect(() => {
    fetchDeliveryFeeSettings();
    fetchLocations();
  }, []);

  // Fetch delivery fee settings
  const fetchDeliveryFeeSettings = async () => {
    setIsLoading(true);
    try {
      const response = await window.ApiClient.getDeliveryFeeSettings();
      if (response.success) {
        setSettings(response.settings || {
          base_fee: 2.99,
          free_delivery_threshold: 0,
          is_enabled: true
        });
      } else {
        setError(response.message || 'Failed to load delivery fee settings');
      }
    } catch (err) {
      console.error('Error fetching delivery fee settings:', err);
      setError('Failed to load delivery fee settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch locations and location-specific fees
  const fetchLocations = async () => {
    try {
      // Fetch all locations
      const locationsResponse = await window.ApiClient.getAdminOrderLocations();
      if (locationsResponse.success) {
        setLocations(locationsResponse.locations || []);
      }

      // Fetch location-specific fees
      const feesResponse = await window.ApiClient.getLocationDeliveryFees();
      if (feesResponse.success) {
        setLocationFees(feesResponse.locationFees || []);
      }
    } catch (err) {
      console.error('Error fetching locations or fees:', err);
      setError('Failed to load locations or fees. Please try again.');
    }
  };

  // Handle form input changes for base settings
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSettings({
      ...settings,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle saving base delivery fee settings
  const handleSaveSettings = async (e) => {
    e.preventDefault();

    setIsSaving(true);
    setError(null);

    try {
      // Prepare data for submission
      const settingsData = {
        ...settings,
        base_fee: parseFloat(settings.base_fee),
        free_delivery_threshold: parseFloat(settings.free_delivery_threshold) || 0
      };

      const response = await window.ApiClient.updateDeliveryFeeSettings(settingsData);

      if (response.success) {
        setSuccess('Delivery fee settings updated successfully');
        setSettings(response.settings);
      } else {
        setError(response.message || 'Failed to update delivery fee settings');
      }
    } catch (err) {
      console.error('Error saving delivery fee settings:', err);
      setError('Failed to save delivery fee settings: ' + (err.message || 'Unknown error'));
    } finally {
      setIsSaving(false);
    }
  };

  // Handle editing location-specific fee
  const handleEditLocationFee = (locationFee) => {
    // Find the location
    const location = locations.find(loc => loc.id === locationFee.location_id);

    if (!location) return;

    // Set modal mode and current location fee
    setModalMode('edit');
    setCurrentLocationFee(locationFee);

    // Initialize form data with current values
    setFormData({
      locationId: locationFee.location_id,
      fee: locationFee.fee.toString(),
      free_delivery_threshold: (locationFee.free_delivery_threshold || '0').toString()
    });

    // Clear any previous form errors
    setFormErrors({});

    // Open the modal
    setIsModalOpen(true);
  };

  // Handle adding a new location-specific fee
  const handleAddLocationFee = () => {
    // Get locations that don't have fees yet
    const locationsWithoutFees = locations.filter(
      location => !locationFees.some(fee => fee.location_id === location.id)
    );

    if (locationsWithoutFees.length === 0) {
      setError('All locations already have delivery fees set');
      return;
    }

    // Set modal mode and reset current location fee
    setModalMode('add');
    setCurrentLocationFee(null);

    // Initialize form data with default values
    setFormData({
      locationId: locationsWithoutFees.length > 0 ? locationsWithoutFees[0].id.toString() : '',
      fee: settings.base_fee.toString(),
      free_delivery_threshold: (settings.free_delivery_threshold || '0').toString()
    });

    // Clear any previous form errors
    setFormErrors({});

    // Open the modal
    setIsModalOpen(true);
  };

  // Handle deleting a location-specific fee
  const handleDeleteLocationFee = (locationFee) => {
    // Set the location fee to delete
    setLocationFeeToDelete(locationFee);

    // Open the delete confirmation modal
    setIsDeleteModalOpen(true);
  };

  // Confirm deletion of a location-specific fee
  const confirmDeleteLocationFee = async () => {
    if (!locationFeeToDelete) return;

    setIsSaving(true);
    setError(null);

    try {
      console.log(`Attempting to delete location fee for locationId: ${locationFeeToDelete.location_id}`);

      const response = await window.ApiClient.deleteLocationDeliveryFee(locationFeeToDelete.location_id);
      console.log('Delete response:', response);

      if (response.success) {
        setSuccess(`Location delivery fee for ${locationFeeToDelete.location_name} removed successfully`);

        // Update the location fees list by removing the deleted fee
        const updatedLocationFees = locationFees.filter(
          fee => fee.location_id !== locationFeeToDelete.location_id
        );

        setLocationFees(updatedLocationFees);
        setIsDeleteModalOpen(false);
        setLocationFeeToDelete(null);
      } else {
        // Keep the modal open if there's an error
        setError(response.message || 'Failed to remove location delivery fee');
      }
    } catch (err) {
      console.error('Error deleting location delivery fee:', err);
      // Show detailed error message
      setError('Failed to remove location delivery fee: ' + (err.message || 'Unknown error'));
    } finally {
      setIsSaving(false);
    }
  };

  // Handle form input changes
  const handleFormInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error for this field when user starts typing
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: null
      });
    }
  };

  // Validate form data
  const validateForm = () => {
    const errors = {};

    // Validate location ID
    if (!formData.locationId) {
      errors.locationId = 'Please select a location';
    }

    // Validate fee
    const fee = parseFloat(formData.fee);
    if (isNaN(fee) || fee < 0) {
      errors.fee = 'Please enter a valid delivery fee (must be a positive number)';
    }

    // Validate threshold
    const threshold = parseFloat(formData.free_delivery_threshold);
    if (isNaN(threshold) || threshold < 0) {
      errors.free_delivery_threshold = 'Please enter a valid threshold (must be a positive number or 0)';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Parse form data
    const locationId = parseInt(formData.locationId);
    const fee = parseFloat(formData.fee);
    const threshold = parseFloat(formData.free_delivery_threshold);

    setIsSaving(true);
    setError(null);

    try {
      const response = await window.ApiClient.updateLocationDeliveryFee(locationId, {
        fee,
        free_delivery_threshold: threshold
      });

      if (response.success) {
        setSuccess('Location delivery fee updated successfully');

        // Update the location fees list
        const updatedLocationFees = [...locationFees];
        const index = updatedLocationFees.findIndex(item => item.location_id === locationId);

        if (index >= 0) {
          // Update existing fee
          updatedLocationFees[index] = response.locationFee;
        } else {
          // Add new fee
          updatedLocationFees.push(response.locationFee);
        }

        setLocationFees(updatedLocationFees);

        // Close the modal
        setIsModalOpen(false);
      } else {
        setError(response.message || 'Failed to update location delivery fee');
      }
    } catch (err) {
      console.error('Error updating location delivery fee:', err);
      setError('Failed to update location delivery fee: ' + (err.message || 'Unknown error'));
    } finally {
      setIsSaving(false);
    }
  };

  // Clear success message after 5 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        setSuccess(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [success]);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <header className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Delivery Fees</h1>
        <p className="mt-1 text-sm text-gray-600">
          Manage delivery fees and free delivery thresholds for all locations
        </p>
      </header>

      {/* Alerts */}
      {error && <ErrorAlert message={error} onClose={() => setError(null)} />}
      {success && <SuccessAlert message={success} onClose={() => setSuccess(null)} />}

      {isLoading ? (
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      ) : (
        <>
          {/* Base Delivery Fee Settings */}
          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Base Delivery Fee Settings</h2>
              <div className="flex items-center">
                <span className="text-sm text-gray-500 mr-2">Enable Delivery Fees</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    id="is_enabled"
                    name="is_enabled"
                    checked={settings.is_enabled}
                    onChange={handleInputChange}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                </label>
              </div>
            </div>

            <form onSubmit={handleSaveSettings} className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Base Delivery Fee */}
                <div>
                  <label htmlFor="base_fee" className="block text-sm font-medium text-gray-700">
                    Base Delivery Fee (₹)
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">₹</span>
                    </div>
                    <input
                      type="number"
                      id="base_fee"
                      name="base_fee"
                      value={settings.base_fee}
                      onChange={handleInputChange}
                      className="pl-7 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                      placeholder="e.g., 2.99"
                      min="0"
                      step="0.01"
                      required
                      disabled={!settings.is_enabled}
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Default delivery fee applied to all orders
                  </p>
                </div>

                {/* Free Delivery Threshold */}
                <div>
                  <label htmlFor="free_delivery_threshold" className="block text-sm font-medium text-gray-700">
                    Free Delivery Threshold (₹)
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 sm:text-sm">₹</span>
                    </div>
                    <input
                      type="number"
                      id="free_delivery_threshold"
                      name="free_delivery_threshold"
                      value={settings.free_delivery_threshold}
                      onChange={handleInputChange}
                      className="pl-7 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                      placeholder="e.g., 500"
                      min="0"
                      step="0.01"
                      disabled={!settings.is_enabled}
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    Orders above this amount qualify for free delivery (0 to disable)
                  </p>
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end pt-4">
                <button
                  type="submit"
                  disabled={isSaving}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSaving ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    <>
                      <span className="material-icons-round text-sm mr-1">save</span>
                      Save Settings
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>

          {/* Location-Specific Delivery Fees */}
          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <div>
                <h2 className="text-lg font-medium text-gray-900">Location-Specific Delivery Fees</h2>
                <p className="text-sm text-gray-500 mt-1">
                  Set custom delivery fees for specific locations
                </p>
              </div>
              <button
                onClick={handleAddLocationFee}
                disabled={isSaving || !settings.is_enabled}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="material-icons-round text-sm mr-1">add</span>
                Add Location Fee
              </button>
            </div>

            {!settings.is_enabled && (
              <div className="bg-yellow-50 p-4 border-b border-yellow-100">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <span className="material-icons-round text-yellow-400">info</span>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      Delivery fees are currently disabled. Enable delivery fees to use location-specific fees.
                    </p>
                  </div>
                </div>
              </div>
            )}

            <DataTable
              columns={columns}
              data={locationFees}
              pagination={false}
              emptyMessage="No location-specific delivery fees configured"
            />
          </div>

          {/* Location Fee Modal */}
          <Modal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            title={modalMode === 'add' ? 'Add Location Delivery Fee' : 'Edit Location Delivery Fee'}
          >
            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              {/* Location Selection */}
              <div>
                <label htmlFor="locationId" className="block text-sm font-medium text-gray-700">
                  Location
                </label>
                <select
                  id="locationId"
                  name="locationId"
                  value={formData.locationId}
                  onChange={handleFormInputChange}
                  disabled={modalMode === 'edit'}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                  required
                >
                  <option value="">Select a location</option>
                  {modalMode === 'add' ? (
                    // Only show locations without fees for adding
                    locations
                      .filter(location => !locationFees.some(fee => fee.location_id === location.id))
                      .map(location => (
                        <option key={location.id} value={location.id}>
                          {location.name}
                        </option>
                      ))
                  ) : (
                    // Show the current location for editing
                    currentLocationFee && (
                      <option value={currentLocationFee.location_id}>
                        {locations.find(loc => loc.id === currentLocationFee.location_id)?.name || 'Unknown Location'}
                      </option>
                    )
                  )}
                </select>
                {formErrors.locationId && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.locationId}</p>
                )}
              </div>

              {/* Delivery Fee */}
              <div>
                <label htmlFor="fee" className="block text-sm font-medium text-gray-700">
                  Delivery Fee (₹)
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">₹</span>
                  </div>
                  <input
                    type="number"
                    id="fee"
                    name="fee"
                    value={formData.fee}
                    onChange={handleFormInputChange}
                    className="pl-7 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                    placeholder="e.g., 2.99"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>
                {formErrors.fee && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.fee}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  Delivery fee for this specific location
                </p>
              </div>

              {/* Free Delivery Threshold */}
              <div>
                <label htmlFor="free_delivery_threshold" className="block text-sm font-medium text-gray-700">
                  Free Delivery Threshold (₹)
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">₹</span>
                  </div>
                  <input
                    type="number"
                    id="free_delivery_threshold"
                    name="free_delivery_threshold"
                    value={formData.free_delivery_threshold}
                    onChange={handleFormInputChange}
                    className="pl-7 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                    placeholder="e.g., 500"
                    min="0"
                    step="0.01"
                  />
                </div>
                {formErrors.free_delivery_threshold && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.free_delivery_threshold}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  Orders above this amount qualify for free delivery (0 to disable)
                </p>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end pt-4 space-x-3">
                <button
                  type="button"
                  onClick={() => setIsModalOpen(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSaving}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSaving ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    <>
                      <span className="material-icons-round text-sm mr-1">save</span>
                      {modalMode === 'add' ? 'Add Fee' : 'Update Fee'}
                    </>
                  )}
                </button>
              </div>
            </form>
          </Modal>

          {/* Delete Confirmation Modal */}
          <Modal
            isOpen={isDeleteModalOpen}
            onClose={() => setIsDeleteModalOpen(false)}
            title="Remove Location Delivery Fee"
            size="sm"
          >
            <div className="p-6 space-y-4">
              {locationFeeToDelete && (
                <div className="text-center">
                  <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                    <span className="material-icons-round text-red-600">delete</span>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Confirm Removal</h3>
                  <p className="text-sm text-gray-500 mb-4">
                    Are you sure you want to remove the delivery fee for <span className="font-medium">{locationFeeToDelete.location_name}</span>?
                  </p>
                  <p className="text-sm text-gray-500 mb-4">
                    This will revert the location to using the base delivery fee.
                  </p>
                  <div className="flex justify-center space-x-3 mt-4">
                    <button
                      type="button"
                      onClick={() => setIsDeleteModalOpen(false)}
                      className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      onClick={confirmDeleteLocationFee}
                      disabled={isSaving}
                      className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSaving ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Removing...
                        </>
                      ) : (
                        <>
                          <span className="material-icons-round text-sm mr-1">delete</span>
                          Remove Fee
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </Modal>
        </>
      )}
    </div>
  );
}
