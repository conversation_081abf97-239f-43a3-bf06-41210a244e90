/**
 * Authentication utilities for Sreekar Publishers
 */

// Helper to generate a JWT token
export async function generateJWT(payload) {
  try {
    // For simplicity, we're using a simple algorithm
    // In production, use a proper JWT library and stronger encryption
    const header = {
      alg: "HS256",
      typ: "JWT"
    };
    
    const now = Math.floor(Date.now() / 1000);
    const expires = now + (60 * 60 * 24 * 30); // 30 days
    
    const tokenPayload = {
      ...payload,
      iat: now,
      exp: expires
    };
    
    const encodedHeader = btoa(JSON.stringify(header));
    const encodedPayload = btoa(JSON.stringify(tokenPayload));
    
    // In real production, use a secure secret from environment variables
    // and a proper HMAC-SHA256 algorithm
    const secret = "SECRET_KEY_CHANGE_IN_PRODUCTION"; 
    const signature = await generateSignature(`${encodedHeader}.${encodedPayload}`, secret);
    
    return `${encodedHeader}.${encodedPayload}.${signature}`;
  } catch (error) {
    console.error('Error generating JWT:', error);
    return null;
  }
}

// Simple signature generator (replace with proper JWT library in production)
async function generateSignature(data, secret) {
  try {
    const encoder = new TextEncoder();
    const encodedData = encoder.encode(data);
    const encodedSecret = encoder.encode(secret);
    
    // Use SubtleCrypto API for HMAC
    const key = await crypto.subtle.importKey(
      "raw",
      encodedSecret,
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign"]
    );
    
    const signature = await crypto.subtle.sign(
      "HMAC", 
      key, 
      encodedData
    );
    
    // Convert to Base64
    return btoa(String.fromCharCode(...new Uint8Array(signature)));
  } catch (error) {
    console.error('Error generating signature:', error);
    return '';
  }
}

// Verify JWT token
export async function verifyJWT(token) {
  try {
    // Split the token and get parts
    const [headerB64, payloadB64, signatureB64] = token.split('.');
    
    // Verify signature
    const secret = "SECRET_KEY_CHANGE_IN_PRODUCTION";
    const expectedSignature = await generateSignature(`${headerB64}.${payloadB64}`, secret);
    
    if (signatureB64 !== expectedSignature) {
      return null; // Invalid signature
    }
    
    // Decode payload
    const payload = JSON.parse(atob(payloadB64));
    
    // Check if token is expired
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < now) {
      return null; // Token expired
    }
    
    return payload;
  } catch (error) {
    console.error('Error verifying JWT:', error);
    return null;
  }
}
