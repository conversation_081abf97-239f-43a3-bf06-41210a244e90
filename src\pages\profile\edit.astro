---
import MainLayout from "../../layouts/MainLayout.astro";
import ProfileEdit from "../../components/profile/ProfileEdit.jsx";
---

<MainLayout
  title="Edit Profile - Sreekar Publishers"
  headerTitle="Edit Profile"
  showHeader={true}
  showBackButton={true}
>
  <ProfileEdit client:load />
</MainLayout>

<script>
  // Check authentication status on page load
  document.addEventListener("DOMContentLoaded", () => {
    // Check if user is authenticated
    if (typeof window.ApiClient !== "undefined") {
      if (!window.ApiClient.isAuthenticated()) {
        // Save current URL to redirect back after login
        const currentPath = window.location.pathname;
        window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`;
      }
    }
  });
</script>
