import React, { useState, useEffect } from 'react';

const CategoryEditForm = ({ category }) => {
  const isEditMode = !!category;
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: ''
  });

  // Initialize form if category is provided (edit mode)
  useEffect(() => {
    if (category) {
      setFormData({
        name: category.name || ''
      });
    }
  }, [category]);

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Form submission handler
  const handleSubmit = async (e) => {
    e.preventDefault();

    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Add default values for icon and color since they're still required by the database
      const dataToSubmit = {
        ...formData,
        icon: '📁', // Default icon
        color: '#808080' // Default color (gray)
      };

      // Determine if this is a create or update operation
      const isNew = !category?.id;
      const url = isNew
        ? '/api/admin/categories'
        : `/api/admin/categories/${category.id}`;

      const method = isNew ? 'POST' : 'PUT';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(dataToSubmit)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save category');
      }

      await response.json();
      setSuccess(true);

      // Redirect to categories page after success
      setTimeout(() => {
        window.location.href = `/admin/categories`;
      }, 1500);

    } catch (err) {
      console.error('Error saving category:', err);
      setError(err.message || 'An error occurred while saving the category.');
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Error/Success Messages */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <span className="material-icons-round text-red-500">error</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <span className="material-icons-round text-green-500">check_circle</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">
                Category {isEditMode ? 'updated' : 'created'} successfully!
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Category Information */}
      <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Category Information</h2>

        <div className="grid grid-cols-1 gap-6">
          {/* Category Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Category Name*
            </label>
            <input
              id="name"
              name="name"
              type="text"
              required
              value={formData.name}
              onChange={handleChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="Enter category name"
            />
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <button
          type="submit"
          disabled={isLoading}
          className={`px-6 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 ${
            isLoading ? 'opacity-70 cursor-not-allowed' : ''
          }`}
        >
          {isLoading ? (
            <span className="flex items-center">
              <span className="material-icons-round animate-spin mr-2 text-sm">refresh</span>
              {isEditMode ? 'Updating...' : 'Creating...'}
            </span>
          ) : (
            <span>{isEditMode ? 'Update Category' : 'Create Category'}</span>
          )}
        </button>
      </div>
    </form>
  );
};

export default CategoryEditForm;
