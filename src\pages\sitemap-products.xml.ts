import type { APIRoute } from 'astro';
import { getProducts } from '../db/database';

export const GET: APIRoute = async ({ locals }) => {
  try {
    const siteUrl = 'https://sreekarpublishers.com';
    const env = locals.runtime.env;
    const today = new Date().toISOString().split('T')[0];
    let sitemapItems = [];

    // Fetch all products for individual product pages
    try {
      // Get all products with a higher limit to capture most/all products
      const products = await getProducts(env, { limit: 5000 });

      // Add product URLs
      for (const product of products) {
        sitemapItems.push(`
  <url>
    <loc>${siteUrl}/product/${product.url_slug}</loc>
    <lastmod>${product.created_at?.split('T')[0] || today}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.9</priority>
  </url>`);
      }

      // Add search URLs for most popular products
      // These are examples - in a production system, you might want to generate these
      // based on actual search analytics
      const popularSearchTerms = ['6th-grade', '7th-grade', '8th-grade', '9th-grade', '10th-grade', 'telugu-medium', 'english-medium'];

      for (const term of popularSearchTerms) {
        sitemapItems.push(`
  <url>
    <loc>${siteUrl}/search/${term}</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`);

        // Add some filtered search combinations
        sitemapItems.push(`
  <url>
    <loc>${siteUrl}/search/${term}/filter/on_sale</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`);

        sitemapItems.push(`
  <url>
    <loc>${siteUrl}/search/${term}/order-by/price-asc</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`);
      }

    } catch (error) {
      console.error('Error fetching products for sitemap:', error);
    }

    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapItems.join('')}
</urlset>`;

    return new Response(xml, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml'
      }
    });
  } catch (error) {
    console.error('Error serving sitemap-products.xml:', error);
    return new Response('Error generating products sitemap', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain'
      }
    });
  }
}