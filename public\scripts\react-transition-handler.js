/**
 * React Component Handler
 * This script ensures React components are properly initialized
 */

// Store references to React component initialization functions
window.ReactComponentHandlers = window.ReactComponentHandlers || {};

// Initialize React components on page load
document.addEventListener('DOMContentLoaded', () => {
  console.log('Page loaded - initializing React components');

  // Call all registered component handlers
  Object.values(window.ReactComponentHandlers).forEach(handler => {
    if (typeof handler === 'function') {
      try {
        handler();
      } catch (error) {
        console.error('Error initializing React component:', error);
      }
    }
  });
});

// Register a component handler
window.registerReactComponent = function(id, initFunction) {
  window.ReactComponentHandlers[id] = initFunction;

  // Initialize immediately if the component is on the current page
  if (typeof initFunction === 'function') {
    try {
      initFunction();
    } catch (error) {
      console.error(`Error initializing React component ${id}:`, error);
    }
  }
};

// Helper function to check if an element exists in the DOM
window.elementExists = function(selector) {
  return document.querySelector(selector) !== null;
};

// Helper function to safely execute code when an element is available
window.whenElementReady = function(selector, callback, maxAttempts = 10) {
  let attempts = 0;

  function checkElement() {
    const element = document.querySelector(selector);
    if (element) {
      callback(element);
      return true;
    }

    attempts++;
    if (attempts >= maxAttempts) {
      console.warn(`Element ${selector} not found after ${maxAttempts} attempts`);
      return false;
    }

    setTimeout(checkElement, 100);
    return false;
  }

  return checkElement();
};
