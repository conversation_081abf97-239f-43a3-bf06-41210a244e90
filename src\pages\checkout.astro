---
import MainLayout from "../layouts/MainLayout.astro";
import Checkout from "../components/checkout.jsx";
---

<MainLayout
  title="Checkout - Sreekar Publishers"
  headerTitle="Checkout"
  showHeader={true}
  showBackButton={true}
>
  <Checkout client:load />
</MainLayout>

<script>
  // Ensure cart badge is updated when the checkout page loads
  document.addEventListener("astro:page-load", () => {
    // Check if user is authenticated
    if (typeof window.ApiClient !== "undefined") {
      if (!window.ApiClient.isAuthenticated()) {
        // Save current URL to redirect back after login
        const currentPath = window.location.pathname;
        window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`;
      }
    }
    
    // Update cart badge
    if (typeof window.CartUtils !== "undefined") {
      window.CartUtils.updateCartBadge();
    }
  });
</script>