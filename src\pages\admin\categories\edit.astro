---
import AdminLayout from "../../../layouts/AdminLayout.astro";
import CategoryEditForm from "../../../components/admin/CategoryEditForm";
export const prerender = false; // Disable prerendering for this page

// Check if we're editing an existing category
const id = Astro.url.searchParams.get("id");
let category = null;

// If ID is provided, fetch the category data
if (id) {
  try {
    const response = await Astro.locals.runtime.env.SNACKSWIFT_DB.prepare(
      "SELECT * FROM categories WHERE id = ?"
    ).bind(id).all();
    
    if (response.results && response.results.length > 0) {
      category = response.results[0];
    }
  } catch (error) {
    console.error("Error fetching category:", error);
  }
}

// Set the page title based on whether we're adding or editing
const pageTitle = category ? `Edit Category: ${category.name}` : "Add New Category";
---

<AdminLayout title={pageTitle}>
  <div class="container px-6 mx-auto">
    <div class="mb-8">
      <a
        href="/admin/categories"
        class="text-orange-600 hover:text-orange-800 flex items-center"
      >
        <span class="material-icons-round text-sm mr-1">arrow_back</span>
        Back to Categories
      </a>
    </div>

    <header class="mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">{pageTitle}</h1>
      <p class="mt-1 text-gray-600">
        {
          category
            ? "Update category details."
            : "Create a new category for your products."
        }
      </p>
    </header>

    <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
      <CategoryEditForm category={category} client:load />
    </div>
  </div>
</AdminLayout>
