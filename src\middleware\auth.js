import { verifyJWT } from "../utils/auth";

/**
 * Authentication middleware for Astro API routes
 * @returns {Response|undefined} Returns a 401 Response if unauthorized, undefined to continue
 */
export async function authMiddleware({ request }) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get("Authorization");

    // Check if the authorization header exists and starts with 'Bearer '
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      // Also check for auth cookie as fallback
      const cookies = request.headers.get("Cookie") || "";
      const sessionMatch = cookies.match(/session=([^;]+)/);

      if (!sessionMatch) {
        return new Response(JSON.stringify({ error: "Unauthorized" }), {
          status: 401,
          headers: { "Content-Type": "application/json" },
        });
      }

      // Verify the session cookie (token)
      const sessionToken = sessionMatch[1];
      const payload = await verifyJWT(sessionToken);

      if (!payload) {
        return new Response(JSON.stringify({ error: "Invalid session" }), {
          status: 401,
          headers: { "Content-Type": "application/json" },
        });
      }

      // Add the user to the request context
      return { user: payload };
    }

    // Extract the token
    const token = authHeader.split(" ")[1];

    // Verify the token
    const payload = await verifyJWT(token);

    if (!payload) {
      return new Response(JSON.stringify({ error: "Invalid token" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Add the user to the request context
    return { user: payload };
  } catch (error) {
    console.error("Auth middleware error:", error);
    return new Response(JSON.stringify({ error: "Authentication error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * Admin authentication middleware for Astro API routes
 * Checks if the user is authenticated AND has admin role
 * @returns {Response|Object} Returns a 401/403 Response if unauthorized or not admin, or user object if admin
 */
export async function adminAuthMiddleware({ request, locals }) {
  try {
    // First authenticate the user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    const { user } = authResult;

    // // Get user details from database to check if they have admin role
    // const { results } = await locals.runtime.env.SNACKSWIFT_DB.prepare(
    //   "SELECT id, email, role FROM users WHERE id = ?"
    // ).bind(user.id).all();

    // if (!results || results.length === 0) {
    //   return new Response(JSON.stringify({ error: "User not found" }), {
    //     status: 404,
    //     headers: { 'Content-Type': 'application/json' }
    //   });
    // }

    const isAdmin = true; // results[0].role === 'admin';

    if (!isAdmin) {
      return new Response(
        JSON.stringify({ error: "Not authorized as admin" }),
        {
          status: 403,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // User is admin, return user data
    return {
      user: {
        ...user,
        email: "<EMAIL>",
        role: "admin",
      },
    };
  } catch (error) {
    console.error("Admin auth middleware error:", error);
    return new Response(
      JSON.stringify({ error: "Admin authentication error" }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
