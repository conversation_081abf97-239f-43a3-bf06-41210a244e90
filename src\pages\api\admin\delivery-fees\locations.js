import { adminAuthMiddleware } from '../../../../middleware/auth';
import {
  getLocationDeliveryFees,
  updateLocationDeliveryFee,
  deleteLocationDeliveryFee,
  getOrderLocationById
} from '../../../../db/database';

export const prerender = false;

/**
 * Get location-specific delivery fees (admin)
 */
export async function GET({ request, locals }) {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get location delivery fees
    const locationFees = await getLocationDeliveryFees(locals.runtime.env);

    return new Response(JSON.stringify({
      success: true,
      locationFees
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error fetching location delivery fees:', error);

    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to fetch location delivery fees',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Update location-specific delivery fee (admin)
 */
export async function POST({ request, locals }) {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Parse request body
    const data = await request.json();

    // Validate required fields
    if (!data.locationId || data.fee === undefined) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Location ID and fee are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if location exists
    const location = await getOrderLocationById(locals.runtime.env, data.locationId);
    if (!location) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Location not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Update location delivery fee
    const locationFee = await updateLocationDeliveryFee(locals.runtime.env, {
      location_id: data.locationId,
      fee: parseFloat(data.fee),
      free_delivery_threshold: parseFloat(data.free_delivery_threshold) || 0
    });

    if (!locationFee) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Failed to update location delivery fee'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      locationFee
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error updating location delivery fee:', error);

    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to update location delivery fee',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Delete location-specific delivery fee (admin)
 */
export async function DELETE({ request, locals }) {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Parse request URL to get location ID
    const url = new URL(request.url);
    const locationId = url.searchParams.get('locationId');

    // Validate location ID
    if (!locationId) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Location ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Delete location delivery fee
    try {
      console.log(`Attempting to delete location fee for locationId: ${locationId}`);

      // Check if the location fee exists before trying to delete it
      const { results: existingFees } = await locals.runtime.env.SNACKSWIFT_DB.prepare(
        "SELECT * FROM location_delivery_fees WHERE location_id = ?"
      ).bind(parseInt(locationId)).all();

      if (!existingFees || existingFees.length === 0) {
        console.log(`No location fee found for locationId: ${locationId}`);
        return new Response(JSON.stringify({
          success: false,
          message: 'Location fee not found'
        }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      console.log(`Found location fee for locationId: ${locationId}, proceeding with deletion`);
      const success = await deleteLocationDeliveryFee(locals.runtime.env, parseInt(locationId));

      if (!success) {
        console.log(`Failed to delete location fee for locationId: ${locationId}`);
        return new Response(JSON.stringify({
          success: false,
          message: 'Failed to delete location delivery fee'
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      console.log(`Successfully deleted location fee for locationId: ${locationId}`);
    } catch (err) {
      console.error(`Error in DELETE handler: ${err.message}`);
      return new Response(JSON.stringify({
        success: false,
        message: 'Error processing delete request',
        error: err.message
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Location delivery fee deleted successfully'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error deleting location delivery fee:', error);

    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to delete location delivery fee',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
