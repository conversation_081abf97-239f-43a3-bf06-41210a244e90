import React, { useState, useEffect } from 'react';
import AdminHeader from '../components/admin/AdminHeader';
import AdminSidebar from '../components/admin/AdminSidebar';

// This component provides the layout structure for all admin pages
export default function AdminLayout({ children }) {
  // State for mobile sidebar visibility
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  
  // Toggle mobile sidebar
  const toggleSidebar = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };
  
  // Close mobile sidebar
  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };
  
  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    function handleClickOutside(event) {
      const sidebar = document.getElementById('admin-sidebar');
      if (sidebar && !sidebar.contains(event.target) && isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMobileMenuOpen]);
  
  // Close sidebar when navigating to a different page
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [children]);
  
  // Close sidebar when pressing escape key
  useEffect(() => {
    function handleEscapeKey(event) {
      if (event.key === 'Escape' && isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    }
    
    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isMobileMenuOpen]);

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div id="admin-sidebar">
        <AdminSidebar 
          isMobileMenuOpen={isMobileMenuOpen} 
          closeMobileMenu={closeMobileMenu} 
        />
      </div>
      
      {/* Mobile overlay */}
      {isMobileMenuOpen && (
        <div 
          className="fixed inset-0 bg-gray-600 bg-opacity-50 z-30 lg:hidden"
          onClick={closeMobileMenu}
          aria-hidden="true"
        ></div>
      )}
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Admin Header */}
        <AdminHeader toggleSidebar={toggleSidebar} />
        
        {/* Main Content Area */}
        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          <div className="max-w-7xl mx-auto">
            {/* Page Content */}
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}