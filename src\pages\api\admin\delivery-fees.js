import { adminAuthMiddleware } from '../../../middleware/auth';
import { 
  getDeliveryFeeSettings, 
  updateDeliveryFeeSettings 
} from '../../../db/database';

export const prerender = false;

/**
 * Get delivery fee settings (admin)
 */
export async function GET({ request, locals }) {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get delivery fee settings
    const settings = await getDeliveryFeeSettings(locals.runtime.env);

    return new Response(JSON.stringify({
      success: true,
      settings
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error fetching delivery fee settings:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to fetch delivery fee settings',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Update delivery fee settings (admin)
 */
export async function POST({ request, locals }) {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Parse request body
    const data = await request.json();

    // Validate required fields
    if (data.base_fee === undefined) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Base fee is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Update settings
    const settings = await updateDeliveryFeeSettings(locals.runtime.env, {
      base_fee: parseFloat(data.base_fee),
      free_delivery_threshold: parseFloat(data.free_delivery_threshold) || 0,
      is_enabled: data.is_enabled !== false
    });

    if (!settings) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Failed to update delivery fee settings'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      settings
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error updating delivery fee settings:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to update delivery fee settings',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
