---
import AdminLayout from "../../../layouts/AdminLayout.astro";
import ProductEditForm from "../../../components/admin/ProductEditForm";
import { getProductBySlug } from "../../../db/database";
export const prerender = false; // Disable prerendering for this page
// Check if we're editing an existing product
const id = Astro.url.searchParams.get("id");
const markUnavailable = Astro.url.searchParams.get("markUnavailable") === "true";
let product = null;

// If ID is provided, fetch the product data
if (id) {
  try {
    product = await getProductBySlug(Astro.locals.runtime.env, id);
    // If markUnavailable is true, set the product as unavailable
    // if (markUnavailable && product) {
    //   product.is_available = false;
    // }
  } catch (error) {
    console.error("Error fetching product:", error);
  }
}

// Set the page title based on whether we're adding or editing
const pageTitle = product ? `Edit Product: ${product.name}` : "Add New Product";
---

<AdminLayout title={pageTitle}>
  <div class="container px-6 mx-auto">
    <div class="mb-8">
      <a
        href="/admin/products"
        class="text-orange-600 hover:text-orange-800 flex items-center"
      >
        <span class="material-icons-round text-sm mr-1">arrow_back</span>
        Back to Products
      </a>
    </div>

    <header class="mb-6">
      <h1 class="text-2xl font-semibold text-gray-800">{pageTitle}</h1>
      <p class="mt-1 text-gray-600">
        {
          product
            ? "Update product details, pricing, and inventory."
            : "Create a new product in your catalog."
        }
      </p>
    </header>

    <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
      <ProductEditForm product={product} client:load />
    </div>
  </div>
</AdminLayout>
