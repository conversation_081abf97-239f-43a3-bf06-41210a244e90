import type { APIRoute } from 'astro';
import { z } from 'zod';
import { adminAuthMiddleware } from '../../../../middleware/auth';

// Schema for validating the request
const requestSchema = z.object({
  period: z.enum(['day', 'week', 'month', 'year']).optional().default('week'),
  category: z.string().optional(),
  stockFilter: z.enum(['all', 'low', 'out']).optional().default('all')
});

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get query params
    const url = new URL(request.url);
    const period = url.searchParams.get('period') || 'week';
    const category = url.searchParams.get('category') || '';
    const stockFilter = url.searchParams.get('stockFilter') || 'all';

    // Validate the parameters
    const parsedParams = requestSchema.safeParse({ period, category, stockFilter });

    if (!parsedParams.success) {
      return new Response(
        JSON.stringify({
          success: false,
          message: 'Invalid request parameters'
        }),
        { status: 400 }
      );
    }

    // Get date range based on period
    const dateRange = getDateRangeForPeriod(period);

    // Get total orders
    const totalOrdersResult = await locals.runtime.env.SNACKSWIFT_DB.prepare(
      'SELECT COUNT(*) as count FROM orders WHERE created_at >= ?'
    ).bind(dateRange.startDate).all();

    const totalOrders = totalOrdersResult.results?.[0]?.count || 0;

    // Get total revenue
    const totalRevenueResult = await locals.runtime.env.SNACKSWIFT_DB.prepare(
      'SELECT SUM(total_amount) as total FROM orders WHERE created_at >= ?'
    ).bind(dateRange.startDate).all();

    const totalRevenue = totalRevenueResult.results?.[0]?.total || 0;

    // Get orders by status
    const ordersByStatusResult = await locals.runtime.env.SNACKSWIFT_DB.prepare(
      'SELECT order_status as status, COUNT(*) as count FROM orders WHERE created_at >= ? GROUP BY order_status'
    ).bind(dateRange.startDate).all();

    // Get pending orders count
    const pendingOrdersResult = await locals.runtime.env.SNACKSWIFT_DB.prepare(
      'SELECT COUNT(*) as count FROM orders WHERE order_status IN (?,?,?) AND created_at >= ?'
    ).bind('placed', 'processing', 'shipped', dateRange.startDate).all();

    const pendingOrders = pendingOrdersResult.results?.[0]?.count || 0;

    // Get completed orders count
    const completedOrdersResult = await locals.runtime.env.SNACKSWIFT_DB.prepare(
      'SELECT COUNT(*) as count FROM orders WHERE order_status = ? AND created_at >= ?'
    ).bind('delivered', dateRange.startDate).all();

    const completedOrders = completedOrdersResult.results?.[0]?.count || 0;

    // Get daily stats for the chart
    const dailyStatsResult = await getDailyStats(locals.runtime.env.SNACKSWIFT_DB, period, dateRange);

    // Get stock requirements based on pending orders (not yet delivered)
    let stockRequirementsQuery = `
      SELECT
        p.id,
        p.name,
        c.name as category_name,
        p.price,
        p.unit_type,
        p.unit_value,
        p.stock_quantity as currentStock,
        SUM(oi.quantity) as pendingQuantity,
        CASE
          WHEN p.stock_quantity <= 0 THEN 'Out of Stock'
          WHEN p.stock_quantity < 10 THEN 'Low Stock'
          ELSE 'In Stock'
        END as stockStatus,
        CASE
          WHEN p.stock_quantity <= 0 THEN SUM(oi.quantity)
          WHEN p.stock_quantity < SUM(oi.quantity) THEN SUM(oi.quantity) - p.stock_quantity
          ELSE 0
        END as stockRequired,
        CASE
          WHEN p.unit_type = 'ml' THEN SUM(oi.quantity * p.unit_value)
          WHEN p.unit_type = 'l' THEN SUM(oi.quantity * p.unit_value * 1000)
          WHEN p.unit_type = 'g' THEN SUM(oi.quantity * p.unit_value)
          WHEN p.unit_type = 'kg' THEN SUM(oi.quantity * p.unit_value * 1000)
          ELSE SUM(oi.quantity)
        END as totalUnitQuantity
      FROM
        products p
      LEFT JOIN
        categories c ON p.category_id = c.id
      LEFT JOIN
        order_items oi ON p.id = oi.product_id
      LEFT JOIN
        orders o ON oi.order_id = o.id
      WHERE
        o.order_status IN ('placed', 'processing', 'shipped')
    `;

    // Add category filter if provided
    const queryParams = [];
    if (category) {
      stockRequirementsQuery += ` AND c.name = ?`;
      queryParams.push(category);
    }

    stockRequirementsQuery += `
      GROUP BY
        p.id
    `;

    // Add stock filter
    if (stockFilter === 'low') {
      stockRequirementsQuery += ` HAVING p.stock_quantity < 10 AND p.stock_quantity > 0`;
    } else if (stockFilter === 'out') {
      stockRequirementsQuery += ` HAVING p.stock_quantity <= 0`;
    }

    stockRequirementsQuery += `
      ORDER BY
        stockRequired DESC, pendingQuantity DESC
    `;

    const stockRequirementsResult = await locals.runtime.env.SNACKSWIFT_DB.prepare(stockRequirementsQuery)
      .bind(...queryParams).all();

    // Return the dashboard stats
    return new Response(
      JSON.stringify({
        success: true,
        stats: {
          totalOrders,
          totalRevenue,
          pendingOrders,
          completedOrders,
          ordersByStatus: ordersByStatusResult.results || [],
          dailyStats: dailyStatsResult,
          stockRequirements: stockRequirementsResult.results || [],
          period,
          category,
          stockFilter
        }
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

  } catch (error) {
    console.error('Error fetching dashboard stats:', error);

    return new Response(
      JSON.stringify({
        success: false,
        message: 'Failed to fetch dashboard statistics'
      }),
      { status: 500 }
    );
  }
};

// Helper function to get date range based on period
function getDateRangeForPeriod(period: string) {
  const now = new Date();
  let startDate = new Date();

  switch (period) {
    case 'day':
      startDate.setDate(now.getDate() - 1);
      break;
    case 'week':
      startDate.setDate(now.getDate() - 7);
      break;
    case 'month':
      startDate.setMonth(now.getMonth() - 1);
      break;
    case 'year':
      startDate.setFullYear(now.getFullYear() - 1);
      break;
    default:
      startDate.setDate(now.getDate() - 7); // Default to week
  }

  return {
    startDate: startDate.toISOString(),
    endDate: now.toISOString()
  };
}

// Helper function to get daily stats for chart
async function getDailyStats(db: any, period: string, dateRange: any) {
  const numDays = getDaysInPeriod(period);

  // For SQL date formatting
  const dateFormat = period === 'year' ? '%Y-%m' : '%Y-%m-%d';

  try {
    // Get daily order counts
    const dailyStats = await db.prepare(`
      SELECT
        strftime(?, created_at) as date,
        COUNT(*) as orderCount,
        SUM(total_amount) as totalRevenue
      FROM
        orders
      WHERE
        created_at >= ?
      GROUP BY
        strftime(?, created_at)
      ORDER BY
        date ASC
    `).bind(dateFormat, dateRange.startDate, dateFormat).all();

    // If using year period, we need to fill in any missing months
    if (period === 'year') {
      return fillMissingMonths(dailyStats.results || []);
    }

    // Otherwise fill in any missing days
    return fillMissingDays(dailyStats.results || [], numDays, period);

  } catch (error) {
    console.error('Error fetching daily stats:', error);
    return [];
  }
}

// Helper function to determine number of days in period
function getDaysInPeriod(period: string): number {
  switch (period) {
    case 'day': return 24; // Hours in a day
    case 'week': return 7;
    case 'month': return 30;
    case 'year': return 12; // Months in a year
    default: return 7;
  }
}

// Helper function to fill in any missing days with zeros
function fillMissingDays(data: any[], numDays: number, period: string): any[] {
  if (!data || data.length === 0) return [];

  const result: any[] = [];
  const now = new Date();
  let startDate: Date;

  // Determine start date based on period
  if (period === 'day') {
    // For day, we use hours
    startDate = new Date(now);
    startDate.setHours(now.getHours() - 24);
  } else if (period === 'week') {
    // For week, go back 7 days
    startDate = new Date(now);
    startDate.setDate(now.getDate() - 6);
    startDate.setHours(0, 0, 0, 0);
  } else {
    // For month, go back 30 days
    startDate = new Date(now);
    startDate.setDate(now.getDate() - 29);
    startDate.setHours(0, 0, 0, 0);
  }

  // Create a map of existing data points
  const dataMap = new Map();
  data.forEach(item => {
    dataMap.set(item.date, item);
  });

  // Fill in all days in the range
  const currentDate = new Date(startDate);

  while (currentDate <= now) {
    const dateStr = currentDate.toISOString().split('T')[0];

    if (dataMap.has(dateStr)) {
      // Use existing data
      result.push(dataMap.get(dateStr));
    } else {
      // Create empty data point
      result.push({
        date: dateStr,
        orderCount: 0,
        totalRevenue: 0
      });
    }

    // Move to next day
    if (period === 'day') {
      currentDate.setHours(currentDate.getHours() + 1);
    } else {
      currentDate.setDate(currentDate.getDate() + 1);
    }
  }

  return result;
}

// Helper function to fill in any missing months with zeros
function fillMissingMonths(data: any[]): any[] {
  if (!data || data.length === 0) return [];

  const result: any[] = [];
  const now = new Date();
  const startDate = new Date(now);
  startDate.setFullYear(now.getFullYear() - 1);

  // Create a map of existing data points
  const dataMap = new Map();
  data.forEach(item => {
    dataMap.set(item.date, item);
  });

  // Fill in all months in the last year
  const currentDate = new Date(startDate);
  currentDate.setDate(1); // Start at the first day of the month

  while (currentDate <= now) {
    const monthStr = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;

    if (dataMap.has(monthStr)) {
      // Use existing data
      result.push(dataMap.get(monthStr));
    } else {
      // Create empty data point
      result.push({
        date: monthStr,
        orderCount: 0,
        totalRevenue: 0
      });
    }

    // Move to next month
    currentDate.setMonth(currentDate.getMonth() + 1);
  }

  return result;
}