import {
  getDeliveryFeeSettings,
  getLocationDeliveryFeeById
} from '../../db/database';

export const prerender = false;

/**
 * Get delivery fee information
 *
 * This endpoint returns:
 * - Base delivery fee settings
 * - Location-specific fee if locationId is provided
 */
export async function GET({ request, locals }) {
  try {
    // Get URL parameters
    const url = new URL(request.url);
    const locationId = url.searchParams.get('location_id') || url.searchParams.get('locationId');

    // Get base delivery fee settings
    const settings = await getDeliveryFeeSettings(locals.runtime.env);

    // If delivery fees are disabled, return zero fees
    if (!settings.is_enabled) {
      return new Response(JSON.stringify({
        success: true,
        base_fee: 0,
        free_delivery_threshold: 0,
        is_enabled: false,
        location_fee: null
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // If no location ID is provided, just return base settings
    if (!locationId) {
      return new Response(JSON.stringify({
        success: true,
        base_fee: settings.base_fee,
        free_delivery_threshold: settings.free_delivery_threshold,
        is_enabled: settings.is_enabled,
        location_fee: null
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get location-specific fee if locationId is provided
    const locationFee = await getLocationDeliveryFeeById(
      locals.runtime.env,
      parseInt(locationId)
    );

    return new Response(JSON.stringify({
      success: true,
      base_fee: settings.base_fee,
      free_delivery_threshold: settings.free_delivery_threshold,
      is_enabled: settings.is_enabled,
      location_fee: locationFee || null
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error fetching delivery fees:', error);

    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to fetch delivery fees',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
