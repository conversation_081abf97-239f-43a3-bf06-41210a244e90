import type { APIRoute } from 'astro';
import { z } from 'zod';
import { adminAuthMiddleware } from '../../../../middleware/auth';

// Schema for validating the request
const requestSchema = z.object({
  period: z.enum(['week', 'month', 'year']).optional().default('month')
});

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get period from query params
    const url = new URL(request.url);
    const period = url.searchParams.get('period') || 'month';
    
    // Validate the period parameter
    const parsedParams = requestSchema.safeParse({ period });
    
    if (!parsedParams.success) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: 'Invalid period parameter' 
        }),
        { status: 400 }
      );
    }

    // Get date range based on period
    const dateRange = getDateRangeForPeriod(period);
    
    // Get sales data by date
    const salesData = await getSalesByDateRange(locals.runtime.env.SNACKSWIFT_DB, dateRange.startDate, dateRange.endDate);
    
    // Get orders by category
    const ordersByCategory = await getOrdersByCategory(locals.runtime.env.SNACKSWIFT_DB, dateRange.startDate);
    
    // Get customer metrics
    const customerMetrics = await getCustomerMetrics(locals.runtime.env.SNACKSWIFT_DB, dateRange.startDate);
    
    // Get popular times
    const popularTimes = await getPopularOrderTimes(locals.runtime.env.SNACKSWIFT_DB, dateRange.startDate);
    
    // Get average order value over time
    const aovTrend = await getAverageOrderValueTrend(locals.runtime.env.SNACKSWIFT_DB, dateRange.startDate, period);

    // Return the analytics data
    return new Response(
      JSON.stringify({
        success: true,
        data: {
          salesData,
          ordersByCategory,
          customerMetrics,
          popularTimes,
          aovTrend,
          period
        }
      }),
      { 
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
  } catch (error) {
    console.error('Error fetching analytics data:', error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: 'Failed to fetch analytics data' 
      }),
      { status: 500 }
    );
  }
};

// Helper function to get date range based on period
function getDateRangeForPeriod(period: string) {
  const now = new Date();
  let startDate = new Date();
  let endDate = new Date(now);
  let previousStartDate = new Date();
  let previousEndDate = new Date(startDate);
  
  switch(period) {
    case 'week':
      startDate.setDate(now.getDate() - 7);
      previousStartDate.setDate(startDate.getDate() - 7);
      previousEndDate = new Date(startDate);
      break;
    case 'month':
      startDate.setMonth(now.getMonth() - 1);
      previousStartDate.setMonth(startDate.getMonth() - 1);
      previousEndDate = new Date(startDate);
      break;
    case 'year':
      startDate.setFullYear(now.getFullYear() - 1);
      previousStartDate.setFullYear(startDate.getFullYear() - 1);
      previousEndDate = new Date(startDate);
      break;
    default:
      startDate.setMonth(now.getMonth() - 1);
      previousStartDate.setMonth(startDate.getMonth() - 1);
      previousEndDate = new Date(startDate);
  }
  
  return {
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0],
    previousStartDate: previousStartDate.toISOString().split('T')[0],
    previousEndDate: previousEndDate.toISOString().split('T')[0]
  };
}

// Function to get sales data by date range
async function getSalesByDateRange(db, startDate, endDate) {
  try {
    // Get current period data
    const { results: currentPeriodSales } = await db.prepare(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as orders,
        SUM(total_amount) as revenue
      FROM orders
      WHERE created_at BETWEEN ? AND ?
      GROUP BY DATE(created_at)
      ORDER BY date
    `).bind(startDate, endDate).all();
    
    // Calculate totals
    const totalOrders = currentPeriodSales?.reduce((sum, day) => sum + day.orders, 0) || 0;
    const totalRevenue = currentPeriodSales?.reduce((sum, day) => sum + day.revenue, 0) || 0;
    
    return {
      dailySales: currentPeriodSales || [],
      totalOrders,
      totalRevenue
    };
  } catch (error) {
    console.error('Error getting sales data:', error);
    return {
      dailySales: [],
      totalOrders: 0,
      totalRevenue: 0
    };
  }
}

// Function to get orders by category
async function getOrdersByCategory(db, startDate) {
  try {
    const { results } = await db.prepare(`
      SELECT 
        c.name as category_name,
        p.category_id,
        COUNT(oi.id) as orderCount,
        SUM(oi.quantity) as itemsSold
      FROM order_items oi
      JOIN products p ON oi.product_id = p.id
      JOIN orders o ON oi.order_id = o.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE o.created_at >= ?
      GROUP BY p.category_id
      ORDER BY itemsSold DESC
    `).bind(startDate).all();
    
    return results || [];
  } catch (error) {
    console.error('Error getting orders by category:', error);
    return [];
  }
}

// Function to get customer metrics
async function getCustomerMetrics(db, startDate) {
  try {
    // New customers since start date
    const { results: newCustomers } = await db.prepare(`
      SELECT COUNT(*) as count
      FROM users
      WHERE created_at >= ?
    `).bind(startDate).all();
    
    // Customers who placed orders
    const { results: activeCustomers } = await db.prepare(`
      SELECT COUNT(DISTINCT user_id) as count
      FROM orders
      WHERE created_at >= ?
    `).bind(startDate).all();
    
    // Average orders per customer
    const { results: avgOrders } = await db.prepare(`
      SELECT AVG(order_count) as average
      FROM (
        SELECT user_id, COUNT(*) as order_count
        FROM orders
        WHERE created_at >= ?
        GROUP BY user_id
      ) as user_orders
    `).bind(startDate).all();
    
    return {
      newCustomers: newCustomers?.[0]?.count || 0,
      activeCustomers: activeCustomers?.[0]?.count || 0,
      averageOrdersPerCustomer: avgOrders?.[0]?.average || 0
    };
  } catch (error) {
    console.error('Error getting customer metrics:', error);
    return {
      newCustomers: 0,
      activeCustomers: 0,
      averageOrdersPerCustomer: 0
    };
  }
}

// Function to get popular order times
async function getPopularOrderTimes(db, startDate) {
  try {
    // Get order counts by hour of day
    const { results: hourlyData } = await db.prepare(`
      SELECT 
        strftime('%H', created_at) as hour,
        COUNT(*) as orderCount
      FROM orders
      WHERE created_at >= ?
      GROUP BY hour
      ORDER BY hour
    `).bind(startDate).all();
    
    // Get order counts by day of week (0 = Sunday, 6 = Saturday)
    const { results: dailyData } = await db.prepare(`
      SELECT 
        strftime('%w', created_at) as day,
        COUNT(*) as orderCount
      FROM orders
      WHERE created_at >= ?
      GROUP BY day
      ORDER BY day
    `).bind(startDate).all();
    
    return {
      hourly: hourlyData || [],
      daily: dailyData || []
    };
  } catch (error) {
    console.error('Error getting popular order times:', error);
    return {
      hourly: [],
      daily: []
    };
  }
}

// Function to get average order value trend
async function getAverageOrderValueTrend(db, startDate, period) {
  try {
    // Format string depends on period
    let format;
    if (period === 'week') {
      format = '%Y-%m-%d'; // Daily for week view
    } else if (period === 'month') {
      format = '%Y-%m-%d'; // Daily for month view
    } else {
      format = '%Y-%m'; // Monthly for year view
    }
    
    const { results } = await db.prepare(`
      SELECT 
        strftime(?, created_at) as date,
        AVG(total_amount) as averageOrderValue,
        COUNT(*) as orderCount
      FROM orders
      WHERE created_at >= ?
      GROUP BY date
      ORDER BY date
    `).bind(format, startDate).all();
    
    return results || [];
  } catch (error) {
    console.error('Error getting AOV trend:', error);
    return [];
  }
}