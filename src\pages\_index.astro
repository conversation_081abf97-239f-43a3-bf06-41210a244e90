---
import MainLayout from "../layouts/MainLayout.astro";
import {
  getCategories,
  getFeaturedProducts,
  getPromotions,
  getPopularProducts,
  getFirstUser
} from "../db/database";

// Disable prerendering to use server-side rendering with Cloudflare D1
export const prerender = false;

// Define fallback data
const fallbackCategories = [
  { id: 1, name: "6th Grade", icon: "📚", color: "#FF9F80" },
  { id: 2, name: "7th Grade", icon: "📖", color: "#E0C094" },
  { id: 3, name: "8th Grade", icon: "📝", color: "#C79F7A" },
  { id: 4, name: "9th Grade", icon: "📊", color: "#8ECAE6" },
  { id: 5, name: "10th Grade", icon: "🎓", color: "#D9BF77" },
];

// Safely fetch all required data for the homepage
let featuredProducts = [];
let popularItems = [];
let categories = [];
let user = { name: "Guest", points: 0, level: "Bronze" };

try {
  if (Astro.locals.runtime && Astro.locals.runtime.env) {
    featuredProducts = await getFeaturedProducts(Astro.locals.runtime.env) || [];
    popularItems = await getPopularProducts(Astro.locals.runtime.env) || [];
    categories = await getCategories(Astro.locals.runtime.env) || fallbackCategories;
    user = await getFirstUser(Astro.locals.runtime.env) || user;
  } else {
    console.warn("Runtime environment not available, using fallback data");
    categories = fallbackCategories;
  }
} catch (error) {
  console.error("Error fetching data:", error);
  categories = fallbackCategories;
}
---

<MainLayout showHeader={true} showBackButton={false}>
  <div class="max-w-md mx-auto bg-gray-50 min-h-screen">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white px-6 py-8 mb-6">
      <div class="text-center">
        <h1 class="text-2xl font-bold mb-2">Welcome to Sreekar Publishers</h1>
        <p class="text-blue-100 mb-4">Quality Educational Materials for Grades 6-10</p>
        <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 mb-4">
          <div class="flex items-center justify-between text-sm">
            <div class="text-center">
              <div class="font-semibold text-lg">1000+</div>
              <div class="text-blue-200">Study Materials</div>
            </div>
            <div class="text-center">
              <div class="font-semibold text-lg">50K+</div>
              <div class="text-blue-200">Happy Students</div>
            </div>
            <div class="text-center">
              <div class="font-semibold text-lg">15+</div>
              <div class="text-blue-200">Years Experience</div>
            </div>
          </div>
        </div>
        <!-- Search Bar -->
        <div class="relative">
          <input
            type="text"
            placeholder="Search textbooks, guides, practice sets..."
            class="search-input w-full px-4 py-3 rounded-lg text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-300"
          />
          <button class="absolute right-3 top-3 text-gray-500 hover:text-blue-600 transition-colors">
            <span class="material-icons-round">search</span>
          </button>
        </div>
      </div>
    </section>

    <!-- Grade Categories Section -->
    <section class="px-4 mb-8">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold text-gray-800">Choose Your Grade</h2>
        <a href="/products" class="text-sm text-blue-600 font-medium">View All</a>
      </div>
      <div class="grid grid-cols-2 gap-3">
        {categories.map((category) => (
          <a
            href={`/products?category=${category.id}`}
            class="educational-card bg-white rounded-xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200 active:scale-95"
          >
            <div class="text-center">
              <div class="educational-icon text-3xl mb-2">{category.icon}</div>
              <h3 class="font-semibold text-gray-800 text-sm">{category.name}</h3>
              <p class="text-xs text-gray-500 mt-1">Study Materials</p>
            </div>
          </a>
        ))}
      </div>
    </section>

    <!-- Featured Educational Materials -->
    <section class="px-4 mb-8">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold text-gray-800">Featured Study Materials</h2>
        <a href="/products" class="text-sm text-blue-600 font-medium">View All</a>
      </div>

      {featuredProducts.length > 0 ? (
        <div class="space-y-4">
          {featuredProducts.map((product) => (
            <div class="educational-card bg-white rounded-xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
              <div class="flex gap-4">
                <div class="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                  <img
                    src={product.image}
                    alt={product.name}
                    class="w-full h-full object-cover"
                  />
                </div>
                <div class="flex-1">
                  <div class="flex items-start justify-between mb-2">
                    <h3 class="font-semibold text-gray-800 text-sm leading-tight">{product.name}</h3>
                    {product.is_new && (
                      <span class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">New</span>
                    )}
                  </div>
                  <p class="text-xs text-gray-600 mb-3 line-clamp-2">{product.description}</p>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <span class="text-lg font-bold text-blue-600">{product.price}</span>
                      {product.old_price && (
                        <span class="text-sm text-gray-400 line-through">{product.old_price}</span>
                      )}
                    </div>
                    <button class="btn-primary text-xs px-3 py-1.5">
                      Add to Cart
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div class="bg-white rounded-xl p-6 text-center border border-gray-100">
          <div class="text-gray-400 mb-2">📚</div>
          <p class="text-gray-600 text-sm">Featured products will appear here</p>
        </div>
      )}
    </section>

    <!-- Subject Categories -->
    <section class="px-4 mb-8">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold text-gray-800">Popular Subjects</h2>
        <a href="/products" class="text-sm text-blue-600 font-medium">View All</a>
      </div>
      <div class="grid grid-cols-3 gap-3">
        <div class="educational-card bg-white rounded-lg p-3 text-center shadow-sm border border-gray-100">
          <div class="educational-icon text-2xl mb-1">📐</div>
          <p class="text-xs font-medium text-gray-800">Mathematics</p>
        </div>
        <div class="educational-card bg-white rounded-lg p-3 text-center shadow-sm border border-gray-100">
          <div class="educational-icon text-2xl mb-1">🔬</div>
          <p class="text-xs font-medium text-gray-800">Science</p>
        </div>
        <div class="educational-card bg-white rounded-lg p-3 text-center shadow-sm border border-gray-100">
          <div class="educational-icon text-2xl mb-1">🌍</div>
          <p class="text-xs font-medium text-gray-800">Social Studies</p>
        </div>
        <div class="educational-card bg-white rounded-lg p-3 text-center shadow-sm border border-gray-100">
          <div class="educational-icon text-2xl mb-1">📝</div>
          <p class="text-xs font-medium text-gray-800">English</p>
        </div>
        <div class="educational-card bg-white rounded-lg p-3 text-center shadow-sm border border-gray-100">
          <div class="educational-icon text-2xl mb-1">🕉️</div>
          <p class="text-xs font-medium text-gray-800">Telugu</p>
        </div>
        <div class="educational-card bg-white rounded-lg p-3 text-center shadow-sm border border-gray-100">
          <div class="educational-icon text-2xl mb-1">🎯</div>
          <p class="text-xs font-medium text-gray-800">Practice Sets</p>
        </div>
      </div>
    </section>





    <!-- Popular Study Materials -->
    <section class="px-4 mb-8">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold text-gray-800">Popular Study Materials</h2>
        <a href="/products" class="text-sm text-blue-600 font-medium">View All</a>
      </div>

      {popularItems.length > 0 ? (
        <div class="grid grid-cols-2 gap-3">
          {popularItems.map((item) => (
            <a
              href={`/products/${item.url_slug}`}
              class="educational-card bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200 active:scale-95"
            >
              <div class="relative">
                <img
                  class="w-full aspect-square object-cover"
                  src={item.image}
                  alt={item.name}
                />
                <button class="absolute bottom-2 right-2 w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center shadow-sm hover:bg-blue-700 transition-colors">
                  <span class="material-icons-round text-sm">add</span>
                </button>
              </div>
              <div class="p-3">
                <h3 class="text-sm font-medium text-gray-800 line-clamp-2 leading-snug mb-2">
                  {item.name}
                </h3>
                <div class="flex justify-between items-center">
                  <p class="text-blue-600 font-semibold text-sm">{item.price}</p>
                  {item.rating && (
                    <div class="flex items-center gap-0.5 text-xs text-gray-500">
                      <span class="font-semibold">{item.rating}</span>
                      <span class="text-yellow-400 text-sm">★</span>
                    </div>
                  )}
                </div>
              </div>
            </a>
          ))}
        </div>
      ) : (
        <div class="bg-white rounded-xl p-6 text-center border border-gray-100">
          <div class="text-gray-400 mb-2">📖</div>
          <p class="text-gray-600 text-sm">Popular materials will appear here</p>
        </div>
      )}
    </section>

    <!-- Why Choose Us Section -->
    <section class="px-4 mb-8">
      <h2 class="text-xl font-bold text-gray-800 mb-4">Why Choose Sreekar Publishers?</h2>
      <div class="space-y-3">
        <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <span class="text-blue-600 text-lg">✓</span>
            </div>
            <div>
              <h3 class="font-semibold text-gray-800 text-sm">Quality Content</h3>
              <p class="text-xs text-gray-600">Expertly crafted study materials by experienced educators</p>
            </div>
          </div>
        </div>
        <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
              <span class="text-green-600 text-lg">📚</span>
            </div>
            <div>
              <h3 class="font-semibold text-gray-800 text-sm">Comprehensive Coverage</h3>
              <p class="text-xs text-gray-600">Complete syllabus coverage for grades 6-10</p>
            </div>
          </div>
        </div>
        <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
              <span class="text-purple-600 text-lg">🎯</span>
            </div>
            <div>
              <h3 class="font-semibold text-gray-800 text-sm">Exam Focused</h3>
              <p class="text-xs text-gray-600">Designed to help students excel in their examinations</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section class="px-4 mb-8">
      <div class="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl p-6 text-white text-center">
        <h2 class="text-lg font-bold mb-2">Need Help?</h2>
        <p class="text-blue-100 text-sm mb-4">Our educational experts are here to assist you</p>
        <div class="flex gap-3 justify-center">
          <a href="tel:+919876543210" class="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-lg text-sm font-medium hover:bg-white/30 transition-all duration-200 hover:transform hover:-translate-y-1">
            📞 Call Us
          </a>
          <a href="/contact" class="btn-secondary bg-white text-blue-600 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-100 transition-all duration-200">
            💬 Chat
          </a>
        </div>
      </div>
    </section>
  </div>
</MainLayout>
