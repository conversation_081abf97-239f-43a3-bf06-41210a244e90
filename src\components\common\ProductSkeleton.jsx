import React from "react";

/**
 * A skeleton loading component for products
 * Used as a placeholder while fetching product data
 */
const ProductSkeleton = ({ count = 8 }) => {
  return (
    <div className="w-full animate-pulse">
      {/* Category skeleton */}
      <div className="px-2 pb-1 pt-1">
        <div className="overflow-x-auto scrollbar-hide mb-2">
          <div className="flex space-x-3 py-1">
            {[1, 2, 3, 4, 5].map((item) => (
              <div key={item} className="flex-shrink-0">
                <div className="h-12 w-28 rounded-xl bg-gray-200"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Filter chips skeleton */}
      <div className="px-4 py-2">
        <div className="flex flex-wrap gap-2 mb-4">
          {[1, 2].map((item) => (
            <div key={item} className="h-8 w-24 bg-gray-200 rounded-lg"></div>
          ))}
        </div>
        
        {/* Results stats skeleton */}
        <div className="flex flex-wrap items-center justify-between bg-gray-50 px-4 py-3 rounded-xl mb-4">
          <div className="h-4 w-40 bg-gray-200 rounded-full"></div>
          <div className="h-4 w-28 bg-gray-200 rounded-full"></div>
        </div>
        
        {/* Products grid skeleton */}
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-20">
          {Array(count)
            .fill(0)
            .map((_, index) => (
              <div
                key={`skeleton-${index}`}
                className="product-skeleton bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100"
              >
                <div className="w-full aspect-square bg-gray-200 animate-pulse relative">
                  {index % 3 === 0 && (
                    <div className="absolute top-3 right-3 h-5 w-12 bg-gray-300 rounded-full"></div>
                  )}
                </div>
                <div className="p-4"> 
                  <div className="h-2 w-16 bg-gray-200 rounded-full mb-3"></div>
                  <div className="h-4 w-full bg-gray-200 rounded-full mb-2"></div>
                  <div className="h-4 w-3/4 bg-gray-200 rounded-full mb-3"></div>
                  <div className="flex justify-between items-center pt-2">
                    <div className="h-5 w-16 bg-gray-200 rounded-full"></div>
                    <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                  </div>
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default ProductSkeleton;