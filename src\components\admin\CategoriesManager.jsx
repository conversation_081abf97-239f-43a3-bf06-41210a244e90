import React, { useState, useEffect } from 'react';
import DataTable from './DataTable';

export default function CategoriesManager() {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [search, setSearch] = useState('');
  const [filteredCategories, setFilteredCategories] = useState([]);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/admin/categories');

        if (!response.ok) {
          throw new Error('Failed to fetch categories');
        }

        const data = await response.json();
        setCategories(data.categories || []);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories. Please try again.');
        // Fallback data for development
        setCategories([
          { id: 1, name: 'Main Course' },
          { id: 2, name: 'Appetizers' },
          { id: 3, name: 'Salads' },
          { id: 4, name: 'Desserts' },
          { id: 5, name: 'Beverages' }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Filter categories based on search term
  useEffect(() => {
    if (!search.trim()) {
      setFilteredCategories(categories);
      return;
    }

    const filtered = categories.filter(category =>
      category.name.toLowerCase().includes(search.toLowerCase())
    );
    setFilteredCategories(filtered);
  }, [categories, search]);

  // Handle search
  const handleSearch = (value) => {
    setSearch(value);
  };

  // Handle category deletion
  const handleDeleteCategory = async (categoryId) => {
    if (!window.confirm('Are you sure you want to delete this category?')) {
      return;
    }

    try {
      // Optimistic UI update
      const previousCategories = [...categories];
      setCategories(categories.filter(category => category.id !== categoryId));

      const response = await fetch(`/api/admin/categories/${categoryId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        // Revert to previous state if deletion fails
        setCategories(previousCategories);
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.details || 'Failed to delete category');
      }
    } catch (err) {
      console.error('Error deleting category:', err);
      setError(`Error: ${err.message}`);

      // Clear the error after 5 seconds
      setTimeout(() => {
        setError(null);
      }, 5000);
    }
  };

  const columns = [
    {
      key: 'id',
      name: 'ID',
      render: (row) => (
        <span className="font-medium text-gray-900">#{row.id}</span>
      )
    },
    {
      key: 'name',
      name: 'Name',
      render: (row) => (
        <span className="font-medium text-gray-900">{row.name}</span>
      )
    },

  ];

  return (
    <div className="container px-6 mx-auto">
      <header className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Categories</h1>
          <p className="mt-1 text-gray-600">
            Manage your product categories
          </p>
        </div>
        <a
          href="/admin/categories/edit"
          className="flex items-center bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors shadow-sm"
        >
          <span className="material-icons-round mr-1.5">add</span>
          Add Category
        </a>
      </header>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 overflow-hidden">
        <div className="p-5 border-b border-gray-200">
          <div className="flex flex-wrap items-center gap-4">
            {/* Search */}
            <div className="flex-grow max-w-md">
              <div className="relative">
                <span className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500">
                  <span className="material-icons-round text-base">search</span>
                </span>
                <input
                  type="text"
                  placeholder="Search categories..."
                  value={search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
            </div>
          </div>
        </div>

        {error && (
          <div className="p-4 bg-red-50 border-b border-red-200">
            <div className="flex items-center">
              <span className="material-icons-round text-red-500 mr-2">error_outline</span>
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        <DataTable
          columns={columns}
          data={filteredCategories}
          isLoading={loading}
          pagination={false}
          searchable={false}
          onSearch={handleSearch}
          emptyMessage="No categories found"
          actions={(row) => (
            <div className="flex justify-end space-x-2">
              <a
                href={`/admin/categories/edit?id=${row.id}`}
                className="p-1.5 bg-indigo-50 text-indigo-600 rounded-md hover:bg-indigo-100 transition-colors"
                title="Edit Category"
              >
                <span className="material-icons-round text-sm">edit</span>
              </a>
              <button
                onClick={() => handleDeleteCategory(row.id)}
                className="p-1.5 bg-red-50 text-red-600 rounded-md hover:bg-red-100 transition-colors"
                title="Delete Category"
              >
                <span className="material-icons-round text-sm">delete</span>
              </button>
            </div>
          )}
        />
      </div>
    </div>
  );
}
