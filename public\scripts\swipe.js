/**
 * Swipe Gesture Handler for Sreekar Publishers PWA
 * Enables touch-based swiping for carousels and other elements
 */
window.SwipeHandler = class SwipeHandler {
  constructor(element, options = {}) {
    this.element = element;
    this.options = {
      threshold: options.threshold || 50,
      restraint: options.restraint || 100,
      allowedTime: options.allowedTime || 300,
      onSwipeLeft: options.onSwipeLeft || function() {},
      onSwipeRight: options.onSwipeRight || function() {}
    };

    this.startX = 0;
    this.startY = 0;
    this.startTime = 0;

    this.init();
  }

  init() {
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), false);
    this.element.addEventListener('touchmove', this.handleTouchMove.bind(this), false);
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), false);
  }

  handleTouchStart(e) {
    const firstTouch = e.touches[0];
    this.startX = firstTouch.clientX;
    this.startY = firstTouch.clientY;
    this.startTime = new Date().getTime();
  }

  handleTouchMove(e) {
    // Prevent default scrolling when inside our swipeable element
    if (e.cancelable) {
      e.preventDefault();
    }
  }

  handleTouchEnd(e) {
    const endTime = new Date().getTime();
    const endX = e.changedTouches[0].clientX;
    const endY = e.changedTouches[0].clientY;

    // Time elapsed
    const elapsedTime = endTime - this.startTime;

    // Horizontal distance
    const distX = endX - this.startX;

    // Vertical distance
    const distY = endY - this.startY;

    // Test conditions for valid swipe:
    // 1. Time is less than allowed
    // 2. Horizontal distance exceeds threshold
    // 3. Vertical distance is less than restraint
    if (elapsedTime <= this.options.allowedTime) {
      if (Math.abs(distX) >= this.options.threshold && Math.abs(distY) <= this.options.restraint) {
        if (distX > 0) {
          this.options.onSwipeRight();
        } else {
          this.options.onSwipeLeft();
        }
      }
    }
  }
}

// Apply swipe functionality to carousel elements when DOM is loaded
// and when Astro page transitions occur
function initSwipeHandlers() {
  // Initialize featured products carousel
  const featuredCarousel = document.getElementById('featured-carousel');
  if (featuredCarousel) {
    new window.SwipeHandler(featuredCarousel, {
      onSwipeLeft: () => {
        window.moveCarousel(featuredCarousel, 'next');
      },
      onSwipeRight: () => {
        window.moveCarousel(featuredCarousel, 'prev');
      }
    });
  }

  // Initialize category carousels
  const categoryCarousels = document.querySelectorAll('.category-scroller');
  categoryCarousels.forEach(carousel => {
    new window.SwipeHandler(carousel, {
      onSwipeLeft: () => {
        window.scrollContainer(carousel, 'right');
      },
      onSwipeRight: () => {
        window.scrollContainer(carousel, 'left');
      }
    });
  });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', initSwipeHandlers);

// Helper function to scroll horizontal containers
window.scrollContainer = function(container, direction) {
  const scrollAmount = container.clientWidth * 0.8;
  if (direction === 'left') {
    container.scrollBy({
      left: -scrollAmount,
      behavior: 'smooth'
    });
  } else {
    container.scrollBy({
      left: scrollAmount,
      behavior: 'smooth'
    });
  }
}

// Helper function to move featured carousel
window.moveCarousel = function(carousel, direction) {
  const slides = carousel.querySelectorAll('.carousel-item');
  const activeSlide = carousel.querySelector('.carousel-item.active');

  if (!activeSlide) return;

  let nextIndex = 0;

  slides.forEach((slide, index) => {
    if (slide === activeSlide) {
      if (direction === 'next') {
        nextIndex = (index + 1) % slides.length;
      } else {
        nextIndex = (index - 1 + slides.length) % slides.length;
      }
    }
    slide.classList.remove('active');
  });

  slides[nextIndex].classList.add('active');

  // Update position indicators if they exist
  const indicators = document.querySelectorAll('.carousel-indicator');
  if (indicators.length > 0) {
    indicators.forEach((indicator, index) => {
      if (index === nextIndex) {
        indicator.classList.add('active');
      } else {
        indicator.classList.remove('active');
      }
    });
  }
}