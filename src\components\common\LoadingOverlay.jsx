import React from 'react';
import LoadingSpinner from './LoadingSpinner';

/**
 * Full-screen loading overlay component
 * Used to block UI interaction during processing operations
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isVisible - Whether the overlay is visible
 * @param {string} props.message - Message to display below the spinner
 * @param {string} props.spinnerSize - Size of the spinner (sm, md, lg)
 * @param {string} props.spinnerColor - Color of the spinner
 * @param {string} props.icon - Optional material icon to display above the message
 */
export default function LoadingOverlay({ 
  isVisible = false, 
  message = 'Loading...', 
  spinnerSize = 'lg', 
  spinnerColor = 'blue',
  icon = null
}) {
  if (!isVisible) return null;
  
  return (
    <div className="fixed inset-0 z-50 bg-gray-900 bg-opacity-50 backdrop-blur-sm flex flex-col items-center justify-center transition-all duration-300">
      <div className="bg-white bg-opacity-95 rounded-2xl p-8 max-w-sm w-full mx-4 shadow-xl text-center animate-fade-in">
        <LoadingSpinner size={spinnerSize} color={spinnerColor} />
        
        {icon && (
          <div className="mt-4 mb-2">
            <span className="material-icons-round text-[#5466F7] text-3xl">{icon}</span>
          </div>
        )}
        
        <p className="mt-4 text-gray-800 font-medium text-lg">{message}</p>
        
        <p className="mt-2 text-gray-500 text-sm">
          Please don't close this page or refresh your browser
        </p>
      </div>
    </div>
  );
}
