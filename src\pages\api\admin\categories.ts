import type { APIRoute } from 'astro';
import { getCategories } from '../../../db/database';
export const prerender = false;

export const GET: APIRoute = async ({ locals }) => {
  // Check admin authentication
  // In a production app, uncomment this code
  /*
  if (!locals.user || !locals.user.isAdmin) {
    return new Response(JSON.stringify({
      error: 'Unauthorized'
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
  */

  try {
    const categories = await getCategories(locals.runtime.env);

    return new Response(JSON.stringify({
      categories
    }), {
      headers: {
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('Error fetching categories:', error);
    return new Response(JSON.stringify({
      error: 'Failed to fetch categories',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Create a new category
export const POST: APIRoute = async ({ request, locals }) => {
  // Check admin authentication
  // In a production app, uncomment this code
  /*
  if (!locals.user || !locals.user.isAdmin) {
    return new Response(JSON.stringify({
      error: 'Unauthorized'
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
  */

  try {
    const categoryData = await request.json() as { name?: string; icon?: string; color?: string };
    const env = locals.runtime.env;

    // Validate required fields
    if (!categoryData.name) {
      return new Response(JSON.stringify({
        error: `Missing required field: name`
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Insert the category into the database with default icon and color
    const result = await env.SNACKSWIFT_DB.prepare(`
      INSERT INTO categories (name, icon, color)
      VALUES (?, ?, ?)
    `).bind(
      categoryData.name,
      categoryData.icon || '📁', // Default icon
      categoryData.color || '#808080' // Default color (gray)
    ).run();

    if (!result || !result.success) {
      throw new Error('Failed to create category');
    }

    // Get the newly inserted category ID
    const categoryId = result.meta.last_row_id;

    // Retrieve the newly created category
    const { results } = await env.SNACKSWIFT_DB.prepare(`
      SELECT * FROM categories WHERE id = ?
    `).bind(categoryId).all();

    if (!results || results.length === 0) {
      throw new Error('Category created but unable to retrieve');
    }

    return new Response(JSON.stringify({
      success: true,
      category: results[0],
      message: 'Category created successfully'
    }), {
      status: 201,
      headers: {
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('Error creating category:', error);
    return new Response(JSON.stringify({
      error: 'Failed to create category',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};