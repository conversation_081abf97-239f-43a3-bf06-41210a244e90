import { getCouponByCode, getUserCouponUsage } from '../../../db/database';
import { authMiddleware } from '../../../middleware/auth';

export const prerender = false;

export async function POST({ locals, request }) {
  try {
    const data = await request.json();
    const couponCode = data.code?.trim().toUpperCase();
    
    if (!couponCode) {
      return new Response(JSON.stringify({ error: "Coupon code is required" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // No runtime environment means we can't check the database
    if (!locals.runtime || !locals.runtime.env) {
      return new Response(JSON.stringify({ error: "Runtime environment not available" }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Get coupon details from database
    const coupon = await getCouponByCode(locals.runtime.env, couponCode);
    
    if (!coupon) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: "Invalid or expired coupon code" 
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Check if the order total meets minimum requirement (if provided in request)
    if (data.subtotal && coupon.min_order_amount > 0 && data.subtotal < coupon.min_order_amount) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: `This coupon requires a minimum order of ₹${coupon.min_order_amount}`
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Check user-specific usage limits if the user is authenticated
    let userId = null;
    try {
      const authResult = await authMiddleware({ request });
      if (!(authResult instanceof Response)) {
        userId = authResult.user.id;
        
        // Check if user has already used this coupon
        if (coupon.user_limit > 0) {
          const usageCount = await getUserCouponUsage(locals.runtime.env, couponCode, userId);
          
          if (usageCount >= coupon.user_limit) {
            return new Response(JSON.stringify({ 
              success: false, 
              message: "You've already used this coupon" 
            }), {
              status: 200,
              headers: { 'Content-Type': 'application/json' }
            });
          }
        }
      }
    } catch (error) {
      console.error("Error checking user authentication:", error);
      // Continue without user check if auth fails
    }
    
    // Prepare the coupon response with only the needed details
    const couponResponse = {
      code: coupon.code,
      type: coupon.type,
      value: coupon.value,
      description: coupon.description,
      minOrderAmount: coupon.min_order_amount,
      maxDiscount: coupon.max_discount
    };
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: "Coupon applied successfully!",
      coupon: couponResponse
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error("Error validating coupon:", error);
    return new Response(JSON.stringify({ error: "Failed to validate coupon" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
