/**
 * Page Initialization Handler
 * This script ensures JavaScript is properly initialized on page load
 */

document.addEventListener('DOMContentLoaded', () => {
  console.log('Page loaded - initializing scripts');

  // Initialize iOS status bar spacing
  initStatusBar();

  // Initialize navigation tabs
  initNavTabs();

  // Initialize touch feedback
  initTouchFeedback();

  // Update cart badge
  updateCartBadge();

  // Initialize header scroll behavior
  initHeaderScroll();
});

// Setup for iOS-like status bar spacing
function initStatusBar() {
  const statusBarSpacer = document.getElementById("status-bar-spacer");
  const appHeader = document.getElementById("app-header");

  if (!statusBarSpacer) return;

  // Detect iOS device
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

  // Set status bar height for iOS devices with notch
  if (isIOS) {
    // Get the safe area top inset
    const safeAreaTop = getComputedStyle(
      document.documentElement
    ).getPropertyValue("--safe-area-inset-top");
    if (safeAreaTop && parseInt(safeAreaTop) > 0) {
      statusBarSpacer.style.height = "var(--safe-area-inset-top)";
      if (appHeader) {
        appHeader.style.paddingTop = "var(--safe-area-inset-top)";
        appHeader.style.height = "calc(56px + var(--safe-area-inset-top))";
      }
    } else {
      statusBarSpacer.style.height = "20px"; // Default for older iOS
    }
  } else {
    statusBarSpacer.style.height = "0px";
  }
}

// Native-like Tab Navigation Highlighting
function initNavTabs() {
  const navTabs = document.querySelectorAll(".nav-tab");
  const currentPath = window.location.pathname;

  navTabs.forEach((tab) => {
    const href = tab.getAttribute("href");
    let isActive = false;

    // For home page specifically
    if (href === "/" && (currentPath === "/" || currentPath === "")) {
      isActive = true;
    }
    // For other paths, check if currentPath starts with href (but href is not just /)
    else if (href !== "/" && currentPath.startsWith(href)) {
      isActive = true;
    }

    if (isActive) {
      // Apply active styles
      tab.classList.add("active");
      const navIconContainer = tab.querySelector(".nav-icon-container");
      const navIcon = tab.querySelector(".material-icons-round");
      const navText = tab.querySelector(
        "span:not(.material-icons-round):not([class*='absolute'])"
      );

      if (navIconContainer) {
        navIconContainer.classList.add("bg-[#5466F7]");
        navIconContainer.classList.add("text-white");
      }
      if (navIcon) navIcon.classList.add("text-white");
      if (navText) navText.classList.add("text-[#5466F7]", "font-semibold");
    }
  });
}

// Native touch feedback for all interactive elements
function initTouchFeedback() {
  const touchElements = document.querySelectorAll(
    "button, .nav-tab, a.product-card"
  );

  touchElements.forEach((el) => {
    // Remove existing listeners to prevent duplicates
    el.removeEventListener("touchstart", handleTouchStart);
    el.removeEventListener("touchend", handleTouchEnd);
    el.removeEventListener("touchmove", handleTouchMove);

    // Add touch feedback
    el.addEventListener("touchstart", handleTouchStart);
    el.addEventListener("touchend", handleTouchEnd);
    el.addEventListener("touchmove", handleTouchMove);
  });
}

function handleTouchStart() {
  this.classList.add("touch-active");
}

function handleTouchEnd() {
  this.classList.remove("touch-active");
}

function handleTouchMove() {
  this.classList.remove("touch-active");
}

// Update cart badge
function updateCartBadge() {
  if (window.CartUtils) {
    window.CartUtils.updateCartBadge();
  }
}

// Initialize header scroll behavior
function initHeaderScroll() {
  const header = document.getElementById("app-header");
  if (!header) return;

  let lastScrollTop = 0;

  // Remove existing scroll listener to prevent duplicates
  window.removeEventListener("scroll", handleHeaderScroll);

  // Add scroll behavior for header
  window.addEventListener("scroll", handleHeaderScroll);

  // Initial check
  handleHeaderScroll();

  function handleHeaderScroll() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    // Add shadow when scrolling down
    if (scrollTop > 10) {
      header.classList.add("shadow-sm");
    } else {
      header.classList.remove("shadow-sm");
    }

    lastScrollTop = scrollTop;
  }
}

// Initialize swipe functionality for carousels
function initSwipeHandlers() {
  // Initialize featured products carousel
  const featuredCarousel = document.getElementById('featured-carousel');
  if (featuredCarousel && window.SwipeHandler) {
    new window.SwipeHandler(featuredCarousel, {
      onSwipeLeft: () => {
        if (window.moveCarousel) {
          window.moveCarousel(featuredCarousel, 'next');
        }
      },
      onSwipeRight: () => {
        if (window.moveCarousel) {
          window.moveCarousel(featuredCarousel, 'prev');
        }
      }
    });
  }

  // Initialize category carousels
  const categoryCarousels = document.querySelectorAll('.category-scroller');
  if (window.SwipeHandler) {
    categoryCarousels.forEach(carousel => {
      new window.SwipeHandler(carousel, {
        onSwipeLeft: () => {
          if (window.scrollContainer) {
            window.scrollContainer(carousel, 'right');
          }
        },
        onSwipeRight: () => {
          if (window.scrollContainer) {
            window.scrollContainer(carousel, 'left');
          }
        }
      });
    });
  }
}

// Add initSwipeHandlers to DOMContentLoaded
document.addEventListener('DOMContentLoaded', initSwipeHandlers);
