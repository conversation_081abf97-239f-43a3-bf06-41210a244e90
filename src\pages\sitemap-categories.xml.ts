import type { APIRoute } from 'astro';
import { getCategories } from '../db/database';

export const GET: APIRoute = async ({ locals }) => {
  try {
    const siteUrl = 'https://sreekarpublishers.com';
    const env = locals.runtime.env;
    const today = new Date().toISOString().split('T')[0];
    let sitemapItems = [];

    try {
      // Get all categories
      const categories = await getCategories(env);

      // Add main category page URLs
      for (const category of categories) {
        sitemapItems.push(`
  <url>
    <loc>${siteUrl}/category/${category.id}</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`);

        // Add category with filters
        const filterOptions = ['featured', 'on_sale', 'new_arrivals'];

        // Add filter variations
        for (const filter of filterOptions) {
          sitemapItems.push(`
  <url>
    <loc>${siteUrl}/category/${category.id}/filter/${filter}</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`);
        }

        // Add sort variations
        const sortOptions = ['price-asc', 'price-desc', 'newest', 'bestseller'];
        for (const sort of sortOptions) {
          sitemapItems.push(`
  <url>
    <loc>${siteUrl}/category/${category.id}/order-by/${sort}</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`);
        }

        // Add price range examples
        const priceRanges = ['0-10', '10-20', '20-30', '30-max'];

        for (const range of priceRanges) {
          sitemapItems.push(`
  <url>
    <loc>${siteUrl}/category/${category.id}/price-range/${range}</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`);
        }

        // Add pagination examples for popular categories
        if (['burgers', 'pizzas', 'desserts'].includes(category.slug)) {
          for (let i = 2; i <= 3; i++) {
            sitemapItems.push(`
  <url>
    <loc>${siteUrl}/category/${category.id}/page/${i}</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`);
          }
        }

        // Add a few combined filter examples for key categories
        if (['6th-grade', '10th-grade', 'telugu-medium'].includes(category.slug)) {
          sitemapItems.push(`
  <url>
    <loc>${siteUrl}/category/${category.id}/filter/on_sale/price-range/0-20/order-by/bestseller</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`);

          // Add popular filter combinations with pagination
          sitemapItems.push(`
  <url>
    <loc>${siteUrl}/category/${category.id}/filter/featured/page/2</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.5</priority>
  </url>`);
        }
      }

      // Add search-based URLs
      const commonSearchTerms = ['textbooks', 'workbooks', 'exam-prep', 'study-guides'];

      for (const term of commonSearchTerms) {
        sitemapItems.push(`
  <url>
    <loc>${siteUrl}/search/${encodeURIComponent(term)}</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`);
      }
    } catch (error) {
      console.error('Error fetching categories for sitemap:', error);
    }

    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapItems.join('')}
</urlset>`;

    return new Response(xml, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml'
      }
    });
  } catch (error) {
    console.error('Error serving sitemap-categories.xml:', error);
    return new Response('Error generating categories sitemap', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain'
      }
    });
  }
}