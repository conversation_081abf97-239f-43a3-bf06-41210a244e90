import React, { useState, useEffect } from 'react';
import DataTable from './DataTable';
import Modal from './Modal';
import Toast from './Toast';

export default function LocationsManager() {
  const [locations, setLocations] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('add'); // 'add' or 'edit'
  const [currentLocation, setCurrentLocation] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    is_active: true
  });
  const [toast, setToast] = useState({ show: false, message: '', type: '' });
  const [selectedLocations, setSelectedLocations] = useState([]);

  // Fetch locations on component mount
  useEffect(() => {
    fetchLocations();
  }, []);

  // Fetch locations from API
  const fetchLocations = async () => {
    setIsLoading(true);
    try {
      const response = await window.ApiClient.getAdminOrderLocations();
      if (response.success && response.locations) {
        setLocations(response.locations);
      } else {
        // If the table doesn't exist, show a message but don't treat it as an error
        if (response.error && response.error.includes('no such table')) {
          showToast('The locations feature is new. Add your first location to get started.', 'info');
        } else {
          showToast('Failed to load locations', 'error');
        }
      }
    } catch (error) {
      console.error('Error fetching locations:', error);
      // If the table doesn't exist, show a message but don't treat it as an error
      if (error.message && error.message.includes('no such table')) {
        showToast('The locations feature is new. Add your first location to get started.', 'info');
      } else {
        showToast('Error loading locations: ' + error.message, 'error');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Show toast message
  const showToast = (message, type = 'success') => {
    setToast({ show: true, message, type });
    setTimeout(() => setToast({ show: false, message: '', type: '' }), 3000);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Open modal for adding a new location
  const handleAddLocation = () => {
    setModalMode('add');
    setFormData({
      name: '',
      address: '',
      is_active: true
    });
    setIsModalOpen(true);
  };

  // Open modal for editing an existing location
  const handleEditLocation = (location) => {
    setModalMode('edit');
    setCurrentLocation(location);
    setFormData({
      name: location.name,
      address: location.address,
      is_active: location.is_active
    });
    setIsModalOpen(true);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.name || !formData.address) {
      showToast('Name and address are required', 'error');
      return;
    }

    try {
      let response;

      if (modalMode === 'add') {
        // Create new location
        response = await window.ApiClient.createOrderLocation(formData);
        if (response.success) {
          showToast('Location added successfully', 'success');
          fetchLocations();
        }
      } else {
        // Update existing location
        response = await window.ApiClient.updateOrderLocation(currentLocation.id, formData);
        if (response.success) {
          showToast('Location updated successfully', 'success');
          fetchLocations();
        }
      }

      setIsModalOpen(false);
    } catch (error) {
      console.error('Error saving location:', error);
      showToast('Error saving location: ' + error.message, 'error');
    }
  };

  // Handle location deletion
  const handleDeleteLocation = async (locationId) => {
    if (!confirm('Are you sure you want to delete this location?')) {
      return;
    }

    try {
      const response = await window.ApiClient.deleteOrderLocation(locationId);
      if (response.success) {
        showToast('Location deleted successfully', 'success');
        fetchLocations();
      }
    } catch (error) {
      console.error('Error deleting location:', error);
      showToast('Error deleting location: ' + error.message, 'error');
    }
  };

  // Handle bulk deletion
  const handleBulkDelete = async () => {
    if (selectedLocations.length === 0) {
      showToast('No locations selected', 'error');
      return;
    }

    if (!confirm(`Are you sure you want to delete ${selectedLocations.length} location(s)?`)) {
      return;
    }

    try {
      // Delete each selected location
      for (const locationId of selectedLocations) {
        await window.ApiClient.deleteOrderLocation(locationId);
      }

      showToast(`${selectedLocations.length} location(s) deleted successfully`, 'success');
      setSelectedLocations([]);
      fetchLocations();
    } catch (error) {
      console.error('Error deleting locations:', error);
      showToast('Error deleting locations: ' + error.message, 'error');
    }
  };

  // Table columns configuration
  const columns = [
    {
      id: 'name',
      key: 'name',
      label: 'Name',
      accessorKey: 'name',
      cell: ({ row }) => (
        <div className="font-medium text-gray-900">{row.original.name}</div>
      )
    },
    {
      id: 'address',
      key: 'address',
      label: 'Address',
      accessorKey: 'address',
      cell: ({ row }) => (
        <div className="text-gray-500 max-w-md truncate">{row.original.address}</div>
      )
    },
    {
      id: 'status',
      key: 'is_active',
      label: 'Status',
      accessorKey: 'is_active',
      cell: ({ row }) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          row.original.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {row.original.is_active ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      id: 'actions',
      key: 'actions',
      label: 'Actions',
      cell: ({ row }) => (
        <div className="flex space-x-2">
          <button
            onClick={() => handleEditLocation(row.original)}
            className="text-blue-600 hover:text-blue-900"
          >
            <span className="material-icons-round text-base">edit</span>
          </button>
          <button
            onClick={() => handleDeleteLocation(row.original.id)}
            className="text-red-600 hover:text-red-900"
          >
            <span className="material-icons-round text-base">delete</span>
          </button>
        </div>
      ),
      enableSorting: false,
      width: '100px'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
        <h1 className="text-2xl font-bold text-gray-900">Delivery Locations</h1>
        <div className="flex space-x-3">
          {selectedLocations.length > 0 && (
            <button
              onClick={handleBulkDelete}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <span className="material-icons-round text-sm mr-1">delete</span>
              Delete Selected
            </button>
          )}
          <button
            onClick={handleAddLocation}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            <span className="material-icons-round text-sm mr-1">add</span>
            Add Delivery Location
          </button>
        </div>
      </div>

      {/* Locations Table */}
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <DataTable
          columns={columns}
          data={locations}
          isLoading={isLoading}
          onRowSelectionChange={setSelectedLocations}
          selectedRows={selectedLocations}
          emptyMessage="No locations found"
        />
      </div>

      {/* Add/Edit Location Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={modalMode === 'add' ? 'Add New Delivery Location' : 'Edit Delivery Location'}
      >
        <form onSubmit={handleSubmit} className="space-y-4 p-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Location Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              placeholder="e.g., Eluru City"
              required
            />
          </div>
          <div>
            <label htmlFor="address" className="block text-sm font-medium text-gray-700">
              Area Description
            </label>
            <textarea
              id="address"
              name="address"
              value={formData.address}
              onChange={handleInputChange}
              rows={3}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              placeholder="Detailed area description or boundaries"
              required
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active"
              name="is_active"
              checked={formData.is_active}
              onChange={handleInputChange}
              className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
            />
            <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
              Active for Delivery
            </label>
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={() => setIsModalOpen(false)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              {modalMode === 'add' ? 'Add Delivery Location' : 'Update Delivery Location'}
            </button>
          </div>
        </form>
      </Modal>

      {/* Toast Notification */}
      {toast.show && (
        <Toast message={toast.message} type={toast.type} onClose={() => setToast({ ...toast, show: false })} />
      )}
    </div>
  );
}
