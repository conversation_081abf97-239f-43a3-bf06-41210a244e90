import { getFilteredProducts } from "../../db/database";

export const prerender = false;

export async function GET({ locals, request }) {
  try {
    if (!locals.runtime || !locals.runtime.env) {
      return new Response(
        JSON.stringify({
          error: "Runtime environment not available",
          products: [],
          totalCount: 0,
          currentPage: 1,
          hasMore: false,
        }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
            "Cache-Control": "no-store, max-age=0",
          },
        }
      );
    }

    // Parse query parameters with validation and defaults
    const url = new URL(request.url);

    // Use Number() instead of parseInt for cleaner number conversion
    const page = Math.max(1, Number(url.searchParams.get("page") || 1));
    const limit = 12

    // Extract other parameters
    const category = url.searchParams.get("category") || "all";
    const sort = url.searchParams.get("sort") || "newest";
    const search = url.searchParams.get("search") || "";
    const minPrice = Math.max(0, Number(url.searchParams.get("minPrice") || 0));
    const maxPrice = Math.min(
      1000000,
      Math.max(1, Number(url.searchParams.get("maxPrice") || 10000))
    );

    // Determine if it's a search for an exact product ID
    const isExactSearch = search && !isNaN(parseInt(search));

    // Generate a cache key for consistent caching
    const cacheKey = `products-${category}-${sort}-${search}-${minPrice}-${maxPrice}-${page}-${limit}`; // Removed tags.join()

    // Set different cache durations based on query type
    let cacheDuration = 300; // 5 minutes default

    if (isExactSearch) {
      cacheDuration = 30; // 30 seconds for exact product searches
    }

    console.log(`Cache miss for ${cacheKey}, fetching fresh data`);

    // Fetch products with all the filters
    const result = await getFilteredProducts(locals.runtime.env, {
      page,
      limit,
      category,
      sort,
      search,
      minPrice,
      maxPrice,
      exactMatch: isExactSearch,
    });

    // Ensure consistent response structure
    const responseData = {
      products: result.products || [],
      totalCount: result.totalCount || 0,
      currentPage: page,
      totalPages: result.totalPages || 0,
      hasMore: result.hasMore || false,
      pageSize: limit,
    };

    const response = new Response(JSON.stringify(responseData), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": `public, max-age=${cacheDuration}`,
        ETag: `"${cacheKey}"`,
      },
    });

    return response;
  } catch (error) {
    console.error("API Error fetching products:", error);

    return new Response(
      JSON.stringify({
        error: "Failed to fetch products",
        products: [],
        totalCount: 0,
        currentPage: 1,
        totalPages: 0,
        hasMore: false,
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-store, max-age=0",
        },
      }
    );
  }
}
