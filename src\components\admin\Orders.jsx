import React, { useState, useEffect } from 'react';
import DataTable from './DataTable';
import Modal from '../ui/Modal';

export default function Orders() {
  const [isLoading, setIsLoading] = useState(true);
  const [orders, setOrders] = useState([]);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [orderItems, setOrderItems] = useState([]);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [error, setError] = useState(null);
  const [statusCounts, setStatusCounts] = useState({
    total: 0,
    delivered: 0,
    processing: 0,
    cancelled: 0
  });
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [orderToDelete, setOrderToDelete] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Status filter options
  const statusFilters = [
    { id: 'all', label: 'All Orders' },
    { id: 'placed', label: 'Placed' },
    { id: 'processing', label: 'Processing' },
    { id: 'shipped', label: 'Shipped' },
    { id: 'delivered', label: 'Delivered' },
    { id: 'cancelled', label: 'Cancelled' }
  ];

  const ITEMS_PER_PAGE = 10;

  // Fetch orders data
  useEffect(() => {
    fetchOrders();
  }, [currentPage, activeTab, searchTerm]);

  // Fetch orders from the API
  const fetchOrders = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const status = activeTab !== 'all' ? activeTab : '';

      // Call our API client to get orders with pagination and filtering
      const result = await window.ApiClient.getAdminOrders(
        currentPage,
        ITEMS_PER_PAGE,
        status,
        searchTerm
      );

      // Set orders from the response
      setOrders(result.orders || []);

      // Set total count from pagination data
      if (result.pagination && typeof result.pagination.total === 'number') {
        setTotalCount(result.pagination.total);
      } else {
        // Fallback to length of orders array if pagination data is missing
        setTotalCount(result.orders?.length || 0);
      }

      // Update status counts
      updateStatusCounts();

    } catch (error) {
      console.error('Error fetching orders:', error);
      setError('Failed to load orders. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Update counts for different order statuses
  const updateStatusCounts = async () => {
    try {
      // Get dashboard stats to get order status counts
      const statsResponse = await window.ApiClient.getAdminDashboardStats('month');

      if (statsResponse.success && statsResponse.stats) {
        const { ordersByStatus = [], totalOrders = 0 } = statsResponse.stats;

        // Calculate counts by status
        const processingCount = ordersByStatus
          .filter(item => ['placed', 'processing', 'shipped'].includes(item.status))
          .reduce((sum, item) => sum + item.count, 0);

        const deliveredCount = ordersByStatus
          .find(item => item.status === 'delivered')?.count || 0;

        const cancelledCount = ordersByStatus
          .find(item => item.status === 'cancelled')?.count || 0;

        setStatusCounts({
          total: totalOrders,
          delivered: deliveredCount,
          processing: processingCount,
          cancelled: cancelledCount
        });
      }
    } catch (error) {
      console.error('Error fetching status counts:', error);
    }
  };

  // Fetch order details
  const fetchOrderDetails = async (orderId) => {
    try {
      const orderDetails = await window.ApiClient.getAdminOrderDetails(orderId);

      if (orderDetails.order) {
        return {
          ...orderDetails.order,
          items: orderDetails.items || []
        };
      }

      throw new Error('Order details not found');
    } catch (error) {
      console.error('Error fetching order details:', error);
      return null;
    }
  };

  // Format currency values
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format dates
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Handle order click/view
  const handleViewOrder = (order) => {
    // Navigate to the individual order page
    window.location.href = `/admin/order/${order.id}`;
  };

  // Handle status change
  const handleStatusChange = async (orderId, newStatus) => {
    try {
      setIsUpdatingStatus(true);

      // Update order status via the API
      const result = await window.ApiClient.updateOrderStatus(orderId, newStatus);

      if (result.success) {
        // Update the local state for the selected order
        if (selectedOrder && selectedOrder.id === orderId) {
          setSelectedOrder(prev => ({
            ...prev,
            order_status: newStatus
          }));
        }

        // Update the orders list if the order is in the current view
        setOrders(prevOrders =>
          prevOrders.map(order =>
            order.id === orderId ? { ...order, order_status: newStatus } : order
          )
        );

        // Refetch the counts
        updateStatusCounts();
      } else {
        throw new Error(result.message || 'Failed to update status');
      }
    } catch (error) {
      console.error(`Error updating order ${orderId} status:`, error);
      setError(`Failed to update order status: ${error.message}`);
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Close modal
  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedOrder(null);
    setOrderItems([]);
  };

  // Open delete confirmation modal
  const openDeleteModal = (order) => {
    setOrderToDelete(order);
    setIsDeleteModalOpen(true);
  };

  // Close delete confirmation modal
  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setOrderToDelete(null);
  };

  // Handle order deletion
  const handleDeleteOrder = async () => {
    if (!orderToDelete) return;

    try {
      setIsDeleting(true);
      setError(null);

      // Call API to delete the order
      const result = await window.ApiClient.deleteOrder(orderToDelete.id);

      if (result.success) {
        // Remove the order from the list
        setOrders(prevOrders => prevOrders.filter(order => order.id !== orderToDelete.id));

        // Update status counts
        updateStatusCounts();

        // Close the modal
        closeDeleteModal();
      } else {
        throw new Error(result.error || 'Failed to delete order');
      }
    } catch (error) {
      console.error('Error deleting order:', error);
      setError(`Failed to delete order: ${error.message}`);
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  // Handle search
  const handleSearch = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page on new search
  };

  // Handle tab/filter change
  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    setCurrentPage(1); // Reset to first page on new filter
  };

  // Column configuration for the DataTable
  const columns = [
    { key: 'order_number', label: 'Order #' },
    { key: 'user_name', label: 'Customer' },
    { key: 'created_at', label: 'Date', render: (row) => formatDate(row.created_at) },
    { key: 'total_amount', label: 'Total', render: (row) => formatCurrency(row.total_amount) },
    {
      key: 'location',
      label: 'Delivery Location',
      render: (row) => (
        <span>
          {row.location_address ? row.location_address : 'N/A'}
        </span>
      )
    },
    {
      key: 'order_status',
      label: 'Status',
      render: (row) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          row.order_status === 'delivered' ? 'bg-green-100 text-green-800' :
          row.order_status === 'processing' ? 'bg-blue-100 text-blue-800' :
          row.order_status === 'shipped' ? 'bg-indigo-100 text-indigo-800' :
          row.order_status === 'cancelled' ? 'bg-red-100 text-red-800' :
          'bg-purple-100 text-purple-800'
        }`}>
          {row.order_status.charAt(0).toUpperCase() + row.order_status.slice(1)}
        </span>
      )
    },
    {
      key: 'payment_status',
      label: 'Payment',
      render: (row) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          row.payment_status === 'paid' ? 'bg-green-100 text-green-800' :
          row.payment_status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
          'bg-red-100 text-red-800'
        }`}>
          {row.payment_status?.charAt(0).toUpperCase() + row.payment_status?.slice(1) || 'Unknown'}
        </span>
      )
    },

  ];

  // Show error message if there's an error
  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4 mb-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <span className="material-icons-round text-red-400">error</span>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
            <div className="mt-2">
              <button
                onClick={() => {
                  setError(null);
                  fetchOrders();
                }}
                className="px-3 py-1 text-sm font-medium text-red-800 bg-red-100 rounded-md hover:bg-red-200"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
        <Modal
          title="Delete Order"
          onClose={closeDeleteModal}
        >
          <div className="p-6">
            <div className="mb-4">
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4">
                <span className="material-icons-round text-red-600">delete</span>
              </div>
              <h3 className="text-lg font-medium text-center text-gray-900 mb-2">
                Confirm Order Deletion
              </h3>
              <p className="text-sm text-gray-500 text-center">
                Are you sure you want to delete order #{orderToDelete?.order_number}? This action cannot be undone.
              </p>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={closeDeleteModal}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
                disabled={isDeleting}
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteOrder}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Deleting...
                  </span>
                ) : (
                  'Delete Order'
                )}
              </button>
            </div>
          </div>
        </Modal>
      )}

      {/* Header Section with Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm p-4 flex items-center border border-gray-200">
          <div className="rounded-full bg-blue-50 p-3 mr-4">
            <span className="material-icons-round text-blue-600">receipt_long</span>
          </div>
          <div>
            <p className="text-sm text-gray-500">Total Orders</p>
            <p className="text-xl font-semibold">
              {isLoading ? (
                <span className="text-gray-300">...</span>
              ) : (
                statusCounts.total
              )}
            </p>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 flex items-center border border-gray-200">
          <div className="rounded-full bg-green-50 p-3 mr-4">
            <span className="material-icons-round text-green-600">check_circle</span>
          </div>
          <div>
            <p className="text-sm text-gray-500">Completed</p>
            <p className="text-xl font-semibold">
              {isLoading ? (
                <span className="text-gray-300">...</span>
              ) : (
                statusCounts.delivered
              )}
            </p>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 flex items-center border border-gray-200">
          <div className="rounded-full bg-yellow-50 p-3 mr-4">
            <span className="material-icons-round text-yellow-600">pending</span>
          </div>
          <div>
            <p className="text-sm text-gray-500">Processing</p>
            <p className="text-xl font-semibold">
              {isLoading ? (
                <span className="text-gray-300">...</span>
              ) : (
                statusCounts.processing
              )}
            </p>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 flex items-center border border-gray-200">
          <div className="rounded-full bg-red-50 p-3 mr-4">
            <span className="material-icons-round text-red-600">cancel</span>
          </div>
          <div>
            <p className="text-sm text-gray-500">Cancelled</p>
            <p className="text-xl font-semibold">
              {isLoading ? (
                <span className="text-gray-300">...</span>
              ) : (
                statusCounts.cancelled
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
        <div className="flex flex-col md:flex-row justify-between mb-4">
          {/* Status Tabs */}
          <div className="flex items-center space-x-2 overflow-x-auto pb-2 md:pb-0 mb-3 md:mb-0">
            {statusFilters.map(filter => (
              <button
                key={filter.id}
                onClick={() => handleTabChange(filter.id)}
                className={`px-3 py-1.5 text-sm font-medium rounded-md whitespace-nowrap ${
                  activeTab === filter.id
                    ? 'bg-orange-100 text-orange-800'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {filter.label}
                {filter.id === 'all' && (
                  <span className="ml-1 px-1.5 py-0.5 bg-gray-200 rounded-full text-xs">
                    {totalCount}
                  </span>
                )}
              </button>
            ))}
          </div>

          {/* Search Box */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search by order # or customer..."
              value={searchTerm}
              onChange={handleSearch}
              className="w-full md:w-80 px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
            <span className="absolute right-3 top-2 text-gray-400">
              <span className="material-icons-round">search</span>
            </span>
          </div>
        </div>
      </div>

      {/* Orders Table */}
      <DataTable
        columns={columns}
        data={orders}
        isLoading={isLoading}
        pagination={true}
        paginationConfig={{
          currentPage: currentPage,
          totalItems: totalCount,
          itemsPerPage: ITEMS_PER_PAGE,
          totalPages: Math.ceil(totalCount / ITEMS_PER_PAGE),
          onPageChange: handlePageChange
        }}
        emptyMessage="No orders found"
        actions={(row) => (
          <div className="flex items-center space-x-3 justify-end">
            <a
              href={`/admin/order/${row.id}`}
              className="text-orange-600 hover:text-orange-900 font-medium"
            >
              <span className="material-icons-round">visibility</span>
            </a>
            <button
              onClick={(e) => {
                e.preventDefault();
                openDeleteModal(row);
              }}
              className="text-red-600 hover:text-red-900 font-medium"
              title="Delete Order"
            >
              <span className="material-icons-round">delete</span>
            </button>
          </div>
        )}
      />
    </div>
  );
}