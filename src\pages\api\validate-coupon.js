import { getCouponByCode } from '../../db/database';

export const prerender = false;

/**
 * Validate a coupon code
 */
export async function POST({ request, locals }) {
  try {
    // Parse request body
    const data = await request.json();
    const { code } = data;

    if (!code) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Coupon code is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get coupon by code
    const coupon = await getCouponByCode(locals.runtime.env, code);

    if (!coupon) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Invalid coupon code or coupon has expired'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if coupon has minimum order amount requirement
    if (data.subtotal && coupon.min_order_amount > 0 && data.subtotal < coupon.min_order_amount) {
      return new Response(JSON.stringify({
        success: false,
        message: `This coupon requires a minimum order of ₹${coupon.min_order_amount}`
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      coupon: {
        code: coupon.code,
        type: coupon.type,
        value: coupon.value,
        description: coupon.description,
        minOrderAmount: coupon.min_order_amount,
        maxDiscount: coupon.max_discount
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error validating coupon:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to validate coupon',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
