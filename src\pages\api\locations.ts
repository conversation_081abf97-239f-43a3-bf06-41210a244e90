import type { APIRoute } from 'astro';
import { getOrderLocations } from '../../db/database';

export const prerender = false;

/**
 * Get all active order locations (public)
 */
export const GET: APIRoute = async ({ locals }) => {
  try {
    try {
      // Get all active locations
      const locations = await getOrderLocations(locals.runtime.env);

      return new Response(JSON.stringify({
        success: true,
        locations
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=300' // Cache for 5 minutes
        }
      });
    } catch (dbError) {
      // Check if the error is because the table doesn't exist
      const errorMessage = dbError.toString();
      if (errorMessage.includes('no such table')) {
        return new Response(JSON.stringify({
          success: true,
          locations: [], // Return empty array if table doesn't exist yet
          message: 'Locations feature is being set up'
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Re-throw for other errors
      throw dbError;
    }
  } catch (error) {
    console.error('Error fetching locations:', error);

    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to fetch locations',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
