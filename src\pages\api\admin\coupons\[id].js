import { adminAuthMiddleware } from '../../../../middleware/auth';
import { 
  getCouponById, 
  updateCoupon, 
  deleteCoupon 
} from '../../../../db/database';

export const prerender = false;

/**
 * Get, update, or delete a specific coupon (admin)
 */
export async function GET({ params, request, locals }) {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    const { id } = params;
    
    // Get coupon by ID
    const coupon = await getCouponById(locals.runtime.env, id);
    
    if (!coupon) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Coupon not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      coupon
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error fetching coupon:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to fetch coupon',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Update a coupon (admin)
 */
export async function PUT({ params, request, locals }) {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    const { id } = params;
    
    // Get existing coupon
    const existingCoupon = await getCouponById(locals.runtime.env, id);
    
    if (!existingCoupon) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Coupon not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse request body
    const data = await request.json();

    // Validate required fields
    if (!data.type || (data.type !== 'freeDelivery' && !data.value) || !data.description) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Required fields are missing'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Update coupon
    const updatedCoupon = await updateCoupon(locals.runtime.env, {
      id: existingCoupon.id,
      code: existingCoupon.code, // Code cannot be changed
      type: data.type,
      value: data.type === 'freeDelivery' ? 0 : parseFloat(data.value),
      description: data.description,
      min_order_amount: data.min_order_amount ? parseFloat(data.min_order_amount) : 0,
      max_discount: data.max_discount ? parseFloat(data.max_discount) : null,
      is_active: data.is_active !== false,
      start_date: data.start_date || null,
      end_date: data.end_date || null,
      usage_limit: data.usage_limit ? parseInt(data.usage_limit) : null,
      user_limit: data.user_limit ? parseInt(data.user_limit) : 1
    });

    if (!updatedCoupon) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Failed to update coupon'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      coupon: updatedCoupon
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error updating coupon:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to update coupon',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Delete a coupon (admin)
 */
export async function DELETE({ params, request, locals }) {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    const { id } = params;
    
    // Get existing coupon
    const existingCoupon = await getCouponById(locals.runtime.env, id);
    
    if (!existingCoupon) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Coupon not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Delete coupon
    const success = await deleteCoupon(locals.runtime.env, id);

    if (!success) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Failed to delete coupon'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Coupon deleted successfully'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error deleting coupon:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to delete coupon',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
