import React, { useEffect, useState } from "react";

interface User {
  name: string;
  phone: string;
}

const ProfileManager: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userData, setUserData] = useState<User | null>(null);

  useEffect(() => {
    const loadingTimeout = setTimeout(() => {
      if (loading) {
        console.warn("Loading timeout reached, showing guest view");
        setLoading(false);
      }
    }, 5000);

    const checkAuth = () => {
      try {
        // Check if API client is available and use it to check authentication
        if (typeof window.ApiClient !== "undefined") {
          // Clear the timeout since we have the API client
          clearTimeout(loadingTimeout);

          const isLoggedIn = window.ApiClient.isAuthenticated();
          setIsAuthenticated(isLoggedIn);

          if (isLoggedIn) {
            // Get user data from API client
            const userData = window.ApiClient.getCurrentUser();

            if (userData) {
              setUserData(userData);
            } else {
              console.warn("User is logged in but no user data found");
            }
          }

          setLoading(false);
        } else {
          // API client not immediately available, let's wait a bit
          setTimeout(() => {
            if (typeof window.ApiClient !== "undefined") {
              checkAuth(); // Try again
            } else {
              // API client still not available after wait, fallback to cookie check
              console.warn("API client not available, using fallback authentication check");

              // Clear the timeout
              clearTimeout(loadingTimeout);

              // Check for session cookie as fallback
              const isLoggedIn = document.cookie.includes("session=");
              setIsAuthenticated(isLoggedIn);

              if (isLoggedIn) {
                // Set generic user data
                setUserData({ name: "User", phone: "" });
              }

              setLoading(false);
            }
          }, 1000); // Wait 1 second for API client to be available
        }
      } catch (error) {
        // Error handling
        console.error("Error checking authentication status:", error);
        clearTimeout(loadingTimeout);
        setLoading(false);
        showToast("Couldn't load profile information", "error");
      }
    };

    // Start authentication check
    checkAuth();

    return () => clearTimeout(loadingTimeout);
  }, []);

  const handleLogout = async () => {
    try {
      // Use API client for logout
      if (typeof window.ApiClient !== "undefined") {
        await window.ApiClient.logout();
      }

      // Show toast notification
      showToast("Logged out successfully");

      // Reload page after short delay
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error("Logout failed:", error);
      showToast("Logout failed. Please try again.", "error");
    }
  };

  // Helper function to show toast notifications
  const showToast = (message: string, type = "success") => {
    const toast = document.createElement("div");
    toast.className =
      "fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-3 rounded-xl text-sm font-medium shadow-lg z-50 flex items-center opacity-0 transition-opacity duration-300";

    const icon = document.createElement("span");
    icon.className = "material-icons-round mr-2";

    if (type === "error") {
      icon.textContent = "error";
    } else if (type === "success") {
      icon.textContent = "check_circle";
    } else {
      icon.textContent = "info";
    }

    toast.appendChild(icon);

    const messageSpan = document.createElement("span");
    messageSpan.textContent = message;
    toast.appendChild(messageSpan);

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
      toast.style.opacity = "1";
    }, 10);

    // Remove after delay
    setTimeout(() => {
      toast.style.opacity = "0";
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 300);
    }, 3000);
  };

  // Loading state with skeleton loaders
  if (loading) {
    return (
      <div id="loading-profile" className="flex flex-col items-center py-4">
        {/* Skeleton for profile header */}
        <div className="w-full mb-8">
          <div className="flex items-center">
            {/* Skeleton for avatar */}
            <div className="w-20 h-20 rounded-full bg-gray-200 animate-pulse mr-5"></div>
            <div className="flex-1">
              {/* Skeleton for name */}
              <div className="h-6 bg-gray-200 rounded-md w-3/4 mb-3 animate-pulse"></div>
              {/* Skeleton for phone */}
              <div className="h-4 bg-gray-200 rounded-md w-1/2 animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Skeleton sections */}
        <div className="w-full space-y-6">
          {/* Orders section skeleton */}
          <div className="w-full">
            <div className="h-6 bg-gray-200 rounded-md w-1/3 mb-3 animate-pulse"></div>
            <div className="h-16 bg-gray-200 rounded-xl w-full animate-pulse"></div>
          </div>

          {/* Addresses section skeleton */}
          <div className="w-full">
            <div className="h-6 bg-gray-200 rounded-md w-1/3 mb-3 animate-pulse"></div>
            <div className="h-16 bg-gray-200 rounded-xl w-full animate-pulse"></div>
          </div>

          {/* Favorites section skeleton */}
          <div className="w-full">
            <div className="h-6 bg-gray-200 rounded-md w-1/3 mb-3 animate-pulse"></div>
            <div className="h-16 bg-gray-200 rounded-xl w-full animate-pulse"></div>
          </div>

          {/* Settings section skeleton */}
          <div className="w-full">
            <div className="h-6 bg-gray-200 rounded-md w-1/3 mb-3 animate-pulse"></div>
            <div className="h-32 bg-gray-200 rounded-xl w-full animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  // Guest view
  if (!isAuthenticated) {
    return (
      <div id="guest-view">
        <div className="text-center mb-10 py-4">
          <div
            className="w-24 h-24 bg-gradient-to-br from-orange-50 to-orange-100 rounded-full mx-auto flex items-center justify-center mb-6 shadow-sm"
          >
            <span className="material-icons-round text-[#FF6B35] text-4xl">account_circle</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-3">
            Welcome to Sreekar Publishers
          </h1>
          <p className="text-gray-600 mb-8 max-w-xs mx-auto">
            Login to access your account, track orders and manage your preferences
          </p>

          <a
            href="/login?redirect=/profile"
            className="inline-flex items-center bg-[#FF6B35] text-white py-3.5 px-8 rounded-xl font-medium hover:bg-[#e55c28] transition-all shadow-sm hover:shadow duration-300"
          >
            <span className="material-icons-round mr-2">login</span>
            Login to Your Account
          </a>
        </div>

        <div className="border-t border-gray-200 pt-8 mt-8">
          <h2 className="font-semibold text-gray-800 mb-5 text-lg">
            Continue as Guest
          </h2>
          <div className="space-y-3">
            <a
              href="/"
              className="block bg-white rounded-xl p-4 border border-gray-100 shadow-sm hover:shadow-md transition-all flex items-center"
            >
              <span className="material-icons-round text-[#FF6B35] mr-3">school</span>
              <span className="font-medium text-gray-800">Browse Materials</span>
              <span className="material-icons-round ml-auto text-gray-400">chevron_right</span>
            </a>
            <a
              href="/cart"
              className="block bg-white rounded-xl p-4 border border-gray-100 shadow-sm hover:shadow-md transition-all flex items-center"
            >
              <span className="material-icons-round text-[#FF6B35] mr-3">shopping_cart</span>
              <span className="font-medium text-gray-800">View Cart</span>
              <span className="material-icons-round ml-auto text-gray-400">chevron_right</span>
            </a>
          </div>
        </div>
      </div>
    );
  }

  // User view
  return (
    <div id="user-view">
      {/* Profile Card with refined design */}
      <div
        className="bg-white rounded-xl p-6 border border-gray-100 shadow-md mb-8 relative overflow-hidden"
      >
        <div
          className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-orange-50 to-transparent rounded-bl-full -z-10"
        >
        </div>
        <div className="flex items-center">
          <div
            className="w-20 h-20 rounded-full bg-gradient-to-r from-[#FF6B35] to-[#FF8C66] flex items-center justify-center text-white text-2xl font-bold mr-5 shadow-md"
          >
            <span>{userData?.name ? userData.name.charAt(0).toUpperCase() : 'U'}</span>
          </div>
          <div>
            <h1 className="text-xl font-semibold text-gray-800">
              {userData?.name || 'User'}
            </h1>
            <p className="text-sm text-gray-600 mt-1">
              {userData?.phone || ''}
            </p>
            <a
              href="/profile/edit"
              className="text-sm text-[#FF6B35] hover:text-[#e55c28] font-medium flex items-center mt-2"
            >
              <span className="material-icons-round text-base mr-1">edit</span>
              Edit Profile
            </a>
          </div>
        </div>
      </div>

      {/* Redesigned sections with consistent card styling */}
      <div className="space-y-6">
        <section>
          <h2
            className="text-lg font-semibold text-gray-800 mb-3.5 flex items-center px-1"
          >
            <span className="material-icons-round mr-2.5 text-[#FF6B35]">local_mall</span>
            My Orders
          </h2>
          <div
            className="bg-white rounded-xl overflow-hidden border border-gray-100 shadow-md hover:shadow-lg transition-shadow duration-300"
          >
            <a
              href="/orders"
              className="flex items-center justify-between py-4 px-5"
            >
              <span className="font-medium text-gray-700">Order History</span>
              <div className="flex items-center">
                <span className="material-icons-round text-gray-400">chevron_right</span>
              </div>
            </a>
          </div>
        </section>

        <section>
          <h2
            className="text-lg font-semibold text-gray-800 mb-3.5 flex items-center px-1"
          >
            <span className="material-icons-round mr-2.5 text-[#FF6B35]">location_on</span>
            My Addresses
          </h2>
          <div
            className="bg-white rounded-xl overflow-hidden border border-gray-100 shadow-md hover:shadow-lg transition-shadow duration-300"
          >
            <a
              href="/addresses"
              className="flex items-center justify-between py-4 px-5"
            >
              <span className="font-medium text-gray-700">Delivery Addresses</span>
              <span className="material-icons-round text-gray-400">chevron_right</span>
            </a>
          </div>
        </section>

        <section>
          <h2
            className="text-lg font-semibold text-gray-800 mb-3.5 flex items-center px-1"
          >
            <span className="material-icons-round mr-2.5 text-[#FF6B35]">favorite</span>
            My Favorites
          </h2>
          <div
            className="bg-white rounded-xl overflow-hidden border border-gray-100 shadow-md hover:shadow-lg transition-shadow duration-300"
          >
            <a
              href="/favorites"
              className="flex items-center justify-between py-4 px-5"
            >
              <span className="font-medium text-gray-700">Saved Items</span>
              <span className="material-icons-round text-gray-400">chevron_right</span>
            </a>
          </div>
        </section>

        <section className="mb-24">
          <h2
            className="text-lg font-semibold text-gray-800 mb-3.5 flex items-center px-1"
          >
            <span className="material-icons-round mr-2.5 text-[#FF6B35]">settings</span>
            Account Settings
          </h2>
          <div
            className="bg-white rounded-xl overflow-hidden border border-gray-100 shadow-md"
          >
            <div className="divide-y divide-gray-100">
              <a
                href="/profile/edit"
                className="flex items-center justify-between py-4 px-5 hover:bg-orange-50 transition-colors"
              >
                <div className="flex items-center">
                  <span className="material-icons-round text-gray-500 mr-3.5">person</span>
                  <span className="font-medium text-gray-700">Edit Profile</span>
                </div>
                <span className="material-icons-round text-gray-400">chevron_right</span>
              </a>
              <button
                onClick={handleLogout}
                className="w-full flex items-center justify-between py-4 px-5 text-left text-red-600 hover:bg-red-50 transition-colors"
              >
                <div className="flex items-center">
                  <span className="material-icons-round mr-3.5">logout</span>
                  <span className="font-medium">Logout</span>
                </div>
                <span className="material-icons-round">chevron_right</span>
              </button>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default ProfileManager;

// Add the ApiClient type for TypeScript
declare global {
  interface Window {
    ApiClient: {
      isAuthenticated: () => boolean;
      getCurrentUser: () => any;
      logout: () => Promise<void>;
    };
  }
}
