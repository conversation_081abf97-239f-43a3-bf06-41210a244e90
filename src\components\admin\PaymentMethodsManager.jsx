import React, { useState, useEffect } from 'react';
import LoadingSpinner from '../common/LoadingSpinner';

export default function PaymentMethodsManager() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [settings, setSettings] = useState({
    online_payment_enabled: true,
    cash_on_delivery_enabled: true
  });
  const [hasChanges, setHasChanges] = useState(false);

  // Show notification with proper icon
  const showNotification = (message, type = "success") => {
    if (typeof window !== "undefined") {
      let toastContainer = document.querySelector(".toast-container");

      if (!toastContainer) {
        toastContainer = document.createElement("div");
        toastContainer.className =
          "toast-container fixed bottom-24 sm:bottom-20 left-0 right-0 flex flex-col items-center z-50 pointer-events-none px-4";
        document.body.appendChild(toastContainer);
      }

      const toast = document.createElement("div");
      toast.className =
        "bg-gray-800 text-white px-4 py-3 rounded-xl text-sm font-medium shadow-lg opacity-0 transition-all duration-300 transform translate-y-4 mb-2 flex items-center max-w-md w-full justify-center";

      // Add icon based on type
      const iconName = type === "error" ? "error" : type === "info" ? "info" : "check_circle";
      const iconColor = type === "error" ? "text-red-400" : type === "info" ? "text-blue-400" : "text-green-400";

      toast.innerHTML = `
        <span class="material-icons-round ${iconColor} mr-2">${iconName}</span>
        <span>${message}</span>
      `;

      toastContainer.appendChild(toast);

      // Animate in
      setTimeout(() => {
        toast.classList.add("opacity-100");
        toast.classList.remove("translate-y-4");
      }, 10);

      // Remove after 3 seconds
      setTimeout(() => {
        toast.classList.remove("opacity-100");
        toast.classList.add("opacity-0", "translate-y-4");
        setTimeout(() => toast.remove(), 300);
      }, 3000);
    }
  };

  // Fetch payment method settings
  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    setIsLoading(true);
    try {
      const response = await window.ApiClient.getPaymentMethodSettings();
      if (response.success && response.settings) {
        setSettings(response.settings);
      } else {
        showNotification('Failed to load payment method settings', 'error');
      }
    } catch (error) {
      console.error('Error fetching payment method settings:', error);
      showNotification('Error loading payment method settings', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggle = (setting) => {
    setSettings(prev => {
      const newSettings = {
        ...prev,
        [setting]: !prev[setting]
      };
      setHasChanges(true);
      return newSettings;
    });
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const response = await window.ApiClient.updatePaymentMethodSettings(settings);
      if (response.success) {
        showNotification('Payment method settings updated successfully', 'success');
        setHasChanges(false);
      } else {
        showNotification(response.message || 'Failed to update settings', 'error');
      }
    } catch (error) {
      console.error('Error saving payment method settings:', error);
      showNotification('Error saving payment method settings', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    fetchSettings();
    setHasChanges(false);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" color="blue" />
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Payment Method Settings</h2>
        <p className="text-gray-600">
          Configure which payment methods are available to customers during checkout.
        </p>
      </div>

      <div className="space-y-6">
        {/* Online Payment */}
        <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
          <div>
            <h3 className="font-medium text-gray-800">Online Payment</h3>
            <p className="text-sm text-gray-600">
              Allow customers to pay online via payment gateway
            </p>
          </div>
          <div className="flex items-center">
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.online_payment_enabled}
                onChange={() => handleToggle('online_payment_enabled')}
                disabled={isSaving}
              />
              <div className={`w-11 h-6 bg-gray-300 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer ${
                settings.online_payment_enabled ? 'peer-checked:bg-[#5466F7]' : ''
              } after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all ${
                settings.online_payment_enabled ? 'peer-checked:after:translate-x-full' : ''
              }`}></div>
            </label>
          </div>
        </div>

        {/* Cash on Delivery */}
        <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
          <div>
            <h3 className="font-medium text-gray-800">Cash on Delivery</h3>
            <p className="text-sm text-gray-600">
              Allow customers to pay with cash when order is delivered
            </p>
          </div>
          <div className="flex items-center">
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.cash_on_delivery_enabled}
                onChange={() => handleToggle('cash_on_delivery_enabled')}
                disabled={isSaving}
              />
              <div className={`w-11 h-6 bg-gray-300 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer ${
                settings.cash_on_delivery_enabled ? 'peer-checked:bg-[#5466F7]' : ''
              } after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all ${
                settings.cash_on_delivery_enabled ? 'peer-checked:after:translate-x-full' : ''
              }`}></div>
            </label>
          </div>
        </div>

        {/* Warning if both are disabled */}
        {!settings.online_payment_enabled && !settings.cash_on_delivery_enabled && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-800">
            <div className="flex items-center">
              <span className="material-icons-round mr-2 text-yellow-600">warning</span>
              <p className="font-medium">Warning: All payment methods are disabled</p>
            </div>
            <p className="mt-1 text-sm">
              Customers will not be able to complete checkout if all payment methods are disabled.
              Please enable at least one payment method.
            </p>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
          <button
            onClick={handleReset}
            disabled={!hasChanges || isSaving}
            className={`px-4 py-2 rounded-lg border ${
              !hasChanges || isSaving
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-700 hover:bg-gray-50'
            }`}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={!hasChanges || isSaving}
            className={`px-4 py-2 rounded-lg ${
              !hasChanges || isSaving
                ? 'bg-blue-300 cursor-not-allowed'
                : 'bg-[#5466F7] hover:bg-[#4555e2]'
            } text-white flex items-center`}
          >
            {isSaving ? (
              <>
                <LoadingSpinner size="sm" color="white" />
                <span className="ml-2">Saving...</span>
              </>
            ) : (
              'Save Changes'
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
