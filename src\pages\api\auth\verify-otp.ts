import { verifyOTP, createOrUpdateUser } from '../../../db/database';
import { generateJWT } from '../../../utils/auth';

export const prerender = false;

export async function POST({ request, locals }) {
  try {
    if (!locals.runtime || !locals.runtime.env) {
      return new Response(JSON.stringify({ error: "Runtime environment not available" }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    const { phoneNumber, otpCode } = await request.json();
    
    if (!phoneNumber || !otpCode) {
      return new Response(JSON.stringify({ error: "Phone number and OTP code are required" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Verify OTP
    const isValid = await verifyOTP(locals.runtime.env, phoneNumber, otpCode);
    
    if (!isValid) {
      return new Response(JSON.stringify({ error: "Invalid or expired OTP" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Create or update user
    const user = await createOrUpdateUser(locals.runtime.env, {
      phone_number: phoneNumber,
      is_verified: true
    });
    
    if (!user) {
      return new Response(JSON.stringify({ error: "Failed to create user" }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Generate JWT token with user data (exclude sensitive info)
    const userData = {
      id: user.id,
      name: user.name,
      phone: user.phone_number
    };
    
    const token = await generateJWT(userData);
    
    if (!token) {
      return new Response(JSON.stringify({ error: "Failed to generate authentication token" }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Set HTTP-only cookie with the session token
    // In production, add secure: true for HTTPS
    return new Response(JSON.stringify({ 
      success: true, 
      message: "Authentication successful",
      user: userData,
      token: token // Include token in response for API usage
    }), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Set-Cookie': `session=${token}; HttpOnly; Path=/; SameSite=Strict; Max-Age=${60*60*24*30}` // 30 days
      }
    });
    
  } catch (error) {
    console.error('Error in verify-otp API:', error);
    return new Response(JSON.stringify({ error: "Failed to process request" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
