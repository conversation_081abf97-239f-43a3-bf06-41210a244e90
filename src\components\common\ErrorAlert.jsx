import React from 'react';

/**
 * Error alert component for displaying error messages
 * @param {Object} props - Component props
 * @param {string} props.message - The error message to display
 * @param {Function} props.onClose - Function to call when the close button is clicked
 */
export default function ErrorAlert({ message, onClose }) {
  return (
    <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4 rounded-md">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <span className="material-icons-round text-red-500">error</span>
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-red-800">Error</h3>
          <div className="mt-1 text-sm text-red-700">
            <p>{message}</p>
          </div>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="ml-auto -mx-1.5 -my-1.5 bg-red-50 text-red-500 rounded-lg focus:ring-2 focus:ring-red-400 p-1.5 hover:bg-red-100 inline-flex h-8 w-8 items-center justify-center"
            aria-label="Close"
          >
            <span className="material-icons-round text-sm">close</span>
          </button>
        )}
      </div>
    </div>
  );
}
