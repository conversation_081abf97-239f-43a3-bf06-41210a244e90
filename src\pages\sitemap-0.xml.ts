import type { APIRoute } from 'astro';
import { getProducts, getCategories } from '../db/database';

export const GET: APIRoute = async ({ locals }) => {
  try {
    const siteUrl = 'https://sreekarpublishers.com';
    const env = locals.runtime.env;
    const today = new Date().toISOString().split('T')[0];

    // Static routes that should always be in the sitemap
    const staticPaths = [
      '/',
      '/cart',
      '/checkout',
      '/favorites',
      '/login',
      '/orders',
      '/profile',
      '/addresses'
    ];

    let sitemapItems = staticPaths.map(path => `
  <url>
    <loc>${siteUrl}${path}</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${path === '/' ? '1.0' : '0.7'}</priority>
  </url>`);

    // Fetch categories for category pages
    try {
      const categories = await getCategories(env);

      // Add category URLs using path-based structure
      for (const category of categories) {
        sitemapItems.push(`
  <url>
    <loc>${siteUrl}/category/${category.id}</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`);

        // Add filtered category views using path segments
        sitemapItems.push(`
  <url>
    <loc>${siteUrl}/category/${category.id}/filter/featured</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`);

        sitemapItems.push(`
  <url>
    <loc>${siteUrl}/category/${category.id}/filter/on_sale</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`);
      }
    } catch (error) {
      console.error('Error fetching categories for sitemap:', error);
    }

    // Fetch all products for individual product pages
    try {
      // Get all products with a higher limit to capture most/all products
      const products = await getProducts(env, { limit: 1000 });

      // Add product URLs
      for (const product of products) {
        sitemapItems.push(`
  <url>
    <loc>${siteUrl}/product/${product.url_slug}</loc>
    <lastmod>${product.created_at?.split('T')[0] || today}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.9</priority>
  </url>`);
      }
    } catch (error) {
      console.error('Error fetching products for sitemap:', error);
    }

    // Add specific filter pages that are important for SEO using path segments
    const filterPages = [
      '/filter/featured',
      '/filter/on_sale',
      '/filter/new_arrivals',
      '/order-by/price-asc',
      '/order-by/price-desc',
      '/order-by/newest',
      '/order-by/bestseller'
    ];

    for (const filterPath of filterPages) {
      sitemapItems.push(`
  <url>
    <loc>${siteUrl}${filterPath}</loc>
    <lastmod>${today}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.7</priority>
  </url>`);
    }

    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapItems.join('')}
</urlset>`;

    return new Response(xml, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml'
      }
    });
  } catch (error) {
    console.error('Error serving sitemap-0.xml:', error);
    return new Response('Error generating sitemap', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain'
      }
    });
  }
}