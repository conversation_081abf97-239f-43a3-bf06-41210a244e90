import React, { useState, useEffect } from "react";
import DataTable from "./DataTable";

export default function ProductsManager() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [search, setSearch] = useState("");
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  // Removed unused hasMore state
  const [selectedCategory, setSelectedCategory] = useState("");
  const [categories, setCategories] = useState([]);
  const [sort, setSort] = useState("id");
  const [itemsPerPage] = useState(10);

  // Fetch products directly from the database with filtering
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `/api/admin/products?page=${currentPage}&limit=${itemsPerPage}&search=${search}&category=${selectedCategory}&sort=${sort}`
        );

        if (!response.ok) {
          throw new Error("Failed to fetch products");
        }

        const data = await response.json();
        setProducts(data.products || []);
        setTotalCount(data.totalCount || 0);

        // Check if we're on a page that doesn't exist anymore (e.g., after deleting the last item on a page)
        const totalPages = Math.ceil((data.totalCount || 0) / itemsPerPage);
        if (currentPage > totalPages && totalPages > 0) {
          setCurrentPage(totalPages);
        }
      } catch (err) {
        console.error("Error fetching products:", err);
        setError("Failed to load products. Please try again.");
        // Fallback data for development
        setProducts(generateMockProducts(15));
        setTotalCount(15);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [currentPage, search, selectedCategory, sort, itemsPerPage]);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/admin/categories");

        if (!response.ok) {
          throw new Error("Failed to fetch categories");
        }

        const data = await response.json();
        setCategories(data.categories || []);
      } catch (err) {
        console.error("Error fetching categories:", err);
        // Fallback data
        setCategories([
          { id: "1", name: "Main Course" },
          { id: "2", name: "Appetizers" },
          { id: "3", name: "Salads" },
          { id: "4", name: "Desserts" },
          { id: "5", name: "Beverages" },
        ]);
      }
    };

    fetchCategories();
  }, []);

  // Handle page change - directly use server-side pagination
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
    // The useEffect will trigger a new API call with the updated page
  };

  // Handle search - directly use server-side search
  const handleSearch = (value) => {
    setSearch(value);
    setCurrentPage(1); // Reset to first page on new search
    // The useEffect will trigger a new API call with the updated search term
  };

  // Handle category filter - directly use server-side filtering
  const handleCategoryChange = (e) => {
    setSelectedCategory(e.target.value);
    setCurrentPage(1); // Reset to first page on new filter
    // The useEffect will trigger a new API call with the updated category
  };

  // Handle sort change - directly use server-side sorting
  const handleSortChange = (e) => {
    setSort(e.target.value);
    setCurrentPage(1); // Reset to first page on new sort
    // The useEffect will trigger a new API call with the updated sort
  };

  // Handle product deletion with optimistic UI update
  const handleDeleteProduct = async (productId) => {
    if (!window.confirm("Are you sure you want to delete this product?")) {
      return;
    }

    try {
      // Optimistic UI update
      const previousProducts = [...products];
      setProducts(products.filter((product) => product.id !== productId));

      const response = await fetch(`/api/admin/products/${productId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        // Update total count after successful deletion
        setTotalCount((prev) => prev - 1);
      } else {
        // Revert to previous state if deletion fails
        setProducts(previousProducts);
        const errorData = await response.json();

        // Handle specific error cases
        if (response.status === 409) {
          // This is our custom conflict error for products with orders
          const errorMessage =
            errorData.details ||
            errorData.error ||
            "This product cannot be deleted because it is referenced in orders.";

          // Show a more detailed error message with a suggestion
          const confirmMarkUnavailable = window.confirm(
            `${errorMessage}\n\nWould you like to mark this product as unavailable instead?`
          );

          // If user confirms, redirect to edit page to mark as unavailable
          if (confirmMarkUnavailable) {
            const product = products.find((p) => p.id === productId);
            if (product && product.url_slug) {
              window.location.href = `/admin/products/edit?id=${product.url_slug}&markUnavailable=true`;
              return;
            }
          }
        }

        throw new Error(
          errorData.error || errorData.details || "Failed to delete product"
        );
      }
    } catch (err) {
      console.error("Error deleting product:", err);
      // Use a more user-friendly error display
      setError(`Error: ${err.message}`);

      // Clear the error after 5 seconds
      setTimeout(() => {
        setError(null);
      }, 5000);
    }
  };

  // Generate mock products for development/testing
  const generateMockProducts = (count) => {
    const mockProducts = [];
    const statusOptions = [
      {
        is_featured: true,
        is_new: false,
        is_on_sale: false,
        is_available: true,
      },
      {
        is_featured: false,
        is_new: true,
        is_on_sale: false,
        is_available: true,
      },
      {
        is_featured: false,
        is_new: false,
        is_on_sale: true,
        is_available: true,
      },
      {
        is_featured: false,
        is_new: false,
        is_on_sale: false,
        is_available: false,
      },
      {
        is_featured: true,
        is_new: true,
        is_on_sale: false,
        is_available: true,
      },
    ];

    const categories = [
      "Main Course",
      "Appetizers",
      "Salads",
      "Desserts",
      "Beverages",
    ];

    for (let i = 1; i <= count; i++) {
      const name = `Product ${i}`;
      const status =
        statusOptions[Math.floor(Math.random() * statusOptions.length)];
      const category =
        categories[Math.floor(Math.random() * categories.length)];

      mockProducts.push({
        id: i,
        name,
        url_slug: name.toLowerCase().replace(/\s+/g, "-"),
        price: `₹${Math.floor(Math.random() * 500) + 100}`,
        stock: Math.floor(Math.random() * 100),
        category,
        created_at: new Date(
          Date.now() - Math.random() * 10000000000
        ).toISOString(),
        ...status,
      });
    }

    return mockProducts;
  };

  const columns = [
    {
      key: "id",
      label: "ID",
      sortable: false,
      render: (row) => (
        <span className="font-medium text-gray-900">#{row.id}</span>
      ),
    },
    {
      key: "name",
      label: "Name",
      sortable: false,
      render: (row) => (
        <div className="flex items-center">
          {row.image && (
            <div className="flex-shrink-0 h-10 w-10 rounded-md overflow-hidden bg-gray-100 border border-gray-200">
              <img
                src={row.image}
                alt={row.name}
                className="h-full w-full object-cover"
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src =
                    "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQVNer1ZryNxWVXojlY9Hoyy1-4DVNAmn7lrg&s";
                }}
              />
            </div>
          )}
          <div className="ml-3">
            <div className="font-medium text-gray-900">{row.name || `Product #${row.id}`}</div>
            <div className="text-xs text-gray-500">SKU: {row.id}</div>
          </div>
        </div>
      ),
    },
    {
      key: "category_name",
      label: "Category",
      sortable: false,
      render: (row) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-md text-sm font-medium bg-gray-100 text-gray-800">
          {row.category_name || row.category || "Uncategorized"}
        </span>
      ),
    },
    {
      key: "price",
      label: "Price",
      sortable: true,
      render: (row) => (
        <div>
          <span className="font-medium text-gray-900">₹{row.price}</span>
          {row.old_price && (
            <span className="text-sm text-gray-500 line-through ml-2">
              ₹{row.old_price}
            </span>
          )}
        </div>
      ),
    },
    {
      key: "stock_quantity",
      label: "Stock",
      sortable: true,
      render: (row) => {
        // Get stock value from either stock_quantity or stock field
        const stockValue = row.stock_quantity !== undefined ? row.stock_quantity : (row.stock !== undefined ? row.stock : 0);
        return (
          <div className="flex items-center">
            <span
              className={`font-medium ${
                parseInt(stockValue) === 0 ? "text-red-600" : "text-gray-900"
              }`}
            >
              {stockValue}
            </span>
            {parseInt(stockValue) < 10 && parseInt(stockValue) > 0 && (
              <span className="ml-2 text-xs text-amber-600 bg-amber-50 px-1.5 py-0.5 rounded">
                Low
              </span>
            )}
          </div>
        );
      },
    },
    {
      key: "status",
      label: "Status",
      sortable: false,
      render: (row) => {
        // Get stock value from either stock_quantity or stock field
        const stockValue = row.stock_quantity !== undefined ? row.stock_quantity : (row.stock !== undefined ? row.stock : 0);

        return (
          <div className="flex flex-wrap gap-1">
            {row.is_available === 0 && (
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                Disabled
              </span>
            )}
            {row.is_available === 1 && (
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Available
              </span>
            )}
            {row.is_featured === 1 && (
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Featured
              </span>
            )}
            {row.is_new === 1 && (
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                New
              </span>
            )}
            {row.is_on_sale === 1 && (
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Sale
              </span>
            )}
            {parseInt(stockValue) === 0 && (
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Out of Stock
              </span>
            )}
          </div>
        );
      },
    },

  ];

  return (
    <div className="container px-6 mx-auto">
      <header className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Products</h1>
          <p className="mt-1 text-gray-600">Manage your product catalog</p>
        </div>
        <a
          href="/admin/products/edit"
          className="flex items-center bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors shadow-sm"
        >
          <span className="material-icons-round mr-1.5">add</span>
          Add Product
        </a>
      </header>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 overflow-hidden">
        <div className="p-5 border-b border-gray-200">
          <div className="flex flex-wrap items-center gap-4">
            {/* Search */}
            <div className="flex-grow max-w-md">
              <div className="relative">
                <span className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500">
                  <span className="material-icons-round text-base">search</span>
                </span>
                <input
                  type="text"
                  placeholder="Search products..."
                  value={search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
            </div>

            {/* Category Filter */}
            <div className="w-48">
              <label
                htmlFor="category-filter"
                className="block text-xs font-medium text-gray-700 mb-1"
              >
                Category
              </label>
              <select
                id="category-filter"
                value={selectedCategory}
                onChange={handleCategoryChange}
                className="w-full py-2 px-3 border border-gray-300 bg-white rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Sort Options */}
            <div className="w-48">
              <label
                htmlFor="sort-options"
                className="block text-xs font-medium text-gray-700 mb-1"
              >
                Sort By
              </label>
              <select
                id="sort-options"
                value={sort}
                onChange={handleSortChange}
                className="w-full py-2 px-3 border border-gray-300 bg-white rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="id">ID (Default)</option>
                <option value="price-asc">Price (Low to High)</option>
                <option value="price-desc">Price (High to Low)</option>
                <option value="newest">Newest First</option>
                <option value="name">Name (A-Z)</option>
              </select>
            </div>
          </div>
        </div>

        {error && (
          <div className="p-4 bg-red-50 border-b border-red-200">
            <div className="flex items-center">
              <span className="material-icons-round text-red-500 mr-2">
                error_outline
              </span>
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        <DataTable
          columns={columns}
          data={products}
          isLoading={loading}
          pagination={true}
          paginationConfig={{
            totalItems: totalCount,
            itemsPerPage: itemsPerPage,
            currentPage,
            totalPages: Math.ceil(totalCount / itemsPerPage),
            onPageChange: handlePageChange,
          }}
          searchable={false} // We're handling search externally
          onSearch={handleSearch}
          onSort={(key, direction) => {
            // Map DataTable sort to our API sort format
            let sortValue = "id";
            if (key === "price") {
              sortValue =
                direction === "ascending" ? "price-asc" : "price-desc";
            } else if (key === "id" && direction === "descending") {
              sortValue = "newest";
            } else if (key === "product") {
              sortValue = "name";
            }
            setSort(sortValue);
          }}
          emptyMessage="No products found"
          sortable={true} // Enable sorting on the table
          actions={(row) => (
            <div className="flex justify-end space-x-2">
              <a
                href={`/admin/products/edit?id=${row.url_slug}`}
                className="p-1.5 bg-indigo-50 text-indigo-600 rounded-md hover:bg-indigo-100 transition-colors"
                title="Edit Product"
              >
                <span className="material-icons-round text-sm">edit</span>
              </a>
              <button
                onClick={() => handleDeleteProduct(row.id)}
                className="p-1.5 bg-red-50 text-red-600 rounded-md hover:bg-red-100 transition-colors"
                title="Delete Product"
              >
                <span className="material-icons-round text-sm">delete</span>
              </button>
            </div>
          )}
        />
      </div>
    </div>
  );
}
