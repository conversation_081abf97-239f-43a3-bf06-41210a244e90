import React, { useState, useEffect } from "react";

export default function DataTable({
  columns,
  data,
  pagination = false,
  searchable = false,
  actions = false,
  itemsPerPage = 10,
  isLoading = false,
  emptyMessage = "No data available",
  onSearch,
  onSort,
  onRowSelectionChange,
  selectedRows = [],
  paginationConfig,
  sortable = false,
}) {
  // State for pagination and search
  const [currentPage, setCurrentPage] = useState(
    paginationConfig?.currentPage || 1
  );
  const [searchTerm, setSearchTerm] = useState("");
  const [sortConfig, setSortConfig] = useState({
    key: null,
    direction: "ascending",
  });
  // State for search focus (used in the UI)
  // const [isSearchFocused, setIsSearchFocused] = useState(false);

  // State for row selection
  const [selectedRowIds, setSelectedRowIds] = useState(selectedRows || []);

  // Update selected rows when selectedRows prop changes
  useEffect(() => {
    // Only update if the arrays are different
    if (JSON.stringify(selectedRowIds) !== JSON.stringify(selectedRows)) {
      setSelectedRowIds(selectedRows || []);
    }
  }, [selectedRows, selectedRowIds]);

  // Handle row selection
  const handleRowSelection = (rowId) => {
    let newSelectedRows;
    if (selectedRowIds.includes(rowId)) {
      newSelectedRows = selectedRowIds.filter((id) => id !== rowId);
    } else {
      newSelectedRows = [...selectedRowIds, rowId];
    }
    setSelectedRowIds(newSelectedRows);
    if (onRowSelectionChange) {
      onRowSelectionChange(newSelectedRows);
    }
  };

  // Handle select all rows
  const handleSelectAllRows = () => {
    let newSelectedRows;
    // If all current items are selected, deselect all
    const allSelected = currentItems.every((item) =>
      selectedRowIds.includes(item.id)
    );
    if (allSelected) {
      newSelectedRows = selectedRowIds.filter(
        (id) => !currentItems.some((item) => item.id === id)
      );
    } else {
      // Add all current items that aren't already selected
      const currentItemIds = currentItems.map((item) => item.id);
      newSelectedRows = [...new Set([...selectedRowIds, ...currentItemIds])];
    }
    setSelectedRowIds(newSelectedRows);
    if (onRowSelectionChange) {
      onRowSelectionChange(newSelectedRows);
    }
  };

  // Update currentPage when paginationConfig changes
  useEffect(() => {
    if (paginationConfig?.currentPage) {
      setCurrentPage(paginationConfig.currentPage);
    }
  }, [paginationConfig?.currentPage]);

  // Handle sorting
  const requestSort = (key) => {
    let direction = "ascending";

    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }

    setSortConfig({ key, direction });

    // If external sort handler is provided, use it
    if (onSort) {
      onSort(key, direction);
    }
  };

  // This function is used directly in the input's onChange handler
  // No separate handleSearch function needed

  // Filter data based on search term (only if onSearch is not provided)
  const filteredData = onSearch
    ? data
    : data.filter((item) => {
        if (!searchTerm) return true;

        // Search across all string and number fields
        return Object.keys(item).some((key) => {
          const value = item[key];
          if (typeof value === "string") {
            return value.toLowerCase().includes(searchTerm.toLowerCase());
          }
          if (typeof value === "number") {
            return value.toString().includes(searchTerm);
          }
          return false;
        });
      });

  // Sort data if sortConfig is set (only if onSort is not provided)
  const sortedData = React.useMemo(() => {
    if (onSort) return filteredData; // Skip client-side sorting if server-side sorting is used

    let sortableItems = [...filteredData];
    if (sortConfig.key !== null) {
      sortableItems.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === "ascending" ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === "ascending" ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableItems;
  }, [filteredData, sortConfig, onSort]);

  // Calculate pagination
  const totalItems = paginationConfig?.totalItems || sortedData.length;
  const totalPages =
    paginationConfig?.totalPages || Math.ceil(totalItems / itemsPerPage);

  // Ensure currentPage is within valid range
  useEffect(() => {
    if (totalPages > 0 && currentPage > totalPages) {
      setCurrentPage(1);
      if (paginationConfig?.onPageChange) {
        paginationConfig.onPageChange(1);
      }
    }
  }, [totalPages, currentPage, paginationConfig]);

  // Use server-side pagination if provided, otherwise do client-side pagination
  const currentItems = paginationConfig
    ? data
    : sortedData.slice(
        (currentPage - 1) * itemsPerPage,
        currentPage * itemsPerPage
      );
  // Handle page change
  const paginate = (pageNumber) => {
    setCurrentPage(pageNumber);
    if (paginationConfig?.onPageChange) {
      paginationConfig.onPageChange(pageNumber);
    }
  };

  // Generate loading skeleton with improved design
  const renderLoadingSkeleton = () => {
    return Array.from({ length: Math.min(5, itemsPerPage) }).map(
      (_, rowIndex) => (
        <tr
          key={`loading-row-${rowIndex}`}
          className="animate-pulse border-b border-gray-100"
        >
          {/* Selection checkbox column skeleton */}
          {onRowSelectionChange && (
            <td
              key={`loading-cell-${rowIndex}-checkbox`}
              className="px-6 py-4 whitespace-nowrap w-10"
            >
              <div className="h-4 w-4 bg-gray-200 rounded"></div>
            </td>
          )}

          {/* Regular columns skeleton */}
          {Array.from({ length: columns.length }).map((_, colIndex) => (
            <td
              key={`loading-cell-${rowIndex}-${colIndex}`}
              className="px-6 py-4 whitespace-nowrap"
            >
              <div className="flex flex-col space-y-2">
                <div className="h-4 bg-gray-200 rounded-md w-24"></div>
                {colIndex === 0 && (
                  <div className="h-3 bg-gray-100 rounded-md w-16 mt-1"></div>
                )}
              </div>
            </td>
          ))}

          {/* Actions column skeleton */}
          {actions && (
            <td
              key={`loading-cell-${rowIndex}-actions`}
              className="px-6 py-4 whitespace-nowrap"
            >
              <div className="flex justify-end space-x-2">
                <div className="h-8 w-8 bg-gray-200 rounded-md"></div>
                <div className="h-8 w-8 bg-gray-200 rounded-md"></div>
              </div>
            </td>
          )}
        </tr>
      )
    );
  };

  // Generate empty state with improved design
  const renderEmptyState = () => (
    <tr>
      <td
        colSpan={
          columns.length + (actions ? 1 : 0) + (onRowSelectionChange ? 1 : 0)
        }
        className="px-6 py-16 text-center"
      >
        <div className="flex flex-col items-center justify-center max-w-sm mx-auto">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <span className="material-icons-round text-3xl text-gray-400">
              inventory_2
            </span>
          </div>
          <h3 className="text-lg font-medium text-gray-800 mb-1">
            {emptyMessage}
          </h3>
          <p className="text-gray-500 text-sm text-center">
            {searchTerm
              ? "Try adjusting your search or filters to find what you're looking for."
              : "Add a new item to get started."}
          </p>
        </div>
      </td>
    </tr>
  );

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Table header with search */}
      {searchable && (
        <div className="p-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div className="flex-1 mr-4">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="material-icons-round text-gray-400 text-sm">
                    search
                  </span>
                </div>
                <input
                  type="text"
                  className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 pr-3 py-2 sm:text-sm border-gray-300 rounded-lg shadow-sm"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    if (onSearch) {
                      onSearch(e.target.value);
                    } else {
                      setCurrentPage(1); // Reset to first page on search
                    }
                  }}
                  // onFocus={() => setIsSearchFocused(true)}
                  // onBlur={() => setIsSearchFocused(false)}
                />
              </div>
            </div>
            {actions && (
              <div className="flex space-x-3">
                <button className="bg-white border border-gray-300 rounded-lg py-2 px-4 inline-flex items-center text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
                  <span className="material-icons-round text-sm mr-1.5">
                    filter_list
                  </span>
                  Filter
                </button>
                <button className="bg-indigo-600 border border-transparent rounded-lg py-2 px-4 inline-flex items-center text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors shadow-sm">
                  <span className="material-icons-round text-sm mr-1.5">
                    add
                  </span>
                  Add New
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead>
            <tr className="bg-gray-50">
              {/* Selection checkbox column */}
              {onRowSelectionChange && (
                <th scope="col" className="px-6 py-3.5 w-10">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                      checked={
                        currentItems.length > 0 &&
                        currentItems.every((item) =>
                          selectedRowIds.includes(item.id)
                        )
                      }
                      onChange={handleSelectAllRows}
                    />
                  </div>
                </th>
              )}

              {/* Regular columns */}
              {columns.map((column, colIndex) => (
                <th
                  key={column.id || column.key || `col-${colIndex}`}
                  scope="col"
                  className={`px-6 py-3.5 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider ${
                    column.sortable && sortable
                      ? "cursor-pointer hover:bg-gray-100 transition-colors"
                      : ""
                  }`}
                  onClick={() =>
                    column.sortable && sortable && requestSort(column.key)
                  }
                >
                  <div className="flex items-center">
                    <span>{column.header || column.label}</span>
                    {column.sortable && sortable && (
                      <span className="material-icons-round ml-1 text-xs">
                        {sortConfig.key === column.key
                          ? sortConfig.direction === "ascending"
                            ? "arrow_upward"
                            : "arrow_downward"
                          : "unfold_more"}
                      </span>
                    )}
                  </div>
                </th>
              ))}
              {actions && (
                <th scope="col" className="relative px-6 py-3.5">
                  <span className="sr-only">Actions</span>
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-100">
            {isLoading
              ? renderLoadingSkeleton()
              : currentItems.length > 0
              ? currentItems.map((item, index) => (
                  <tr
                    key={item.id || index}
                    className="hover:bg-gray-50 transition-colors"
                  >
                    {/* Selection checkbox */}
                    {onRowSelectionChange && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 w-10">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                          checked={selectedRowIds.includes(item.id)}
                          onChange={() => handleRowSelection(item.id)}
                        />
                      </td>
                    )}

                    {/* Regular columns */}
                    {columns.map((column, colIndex) => (
                      <td
                        key={`${item.id || index}-${
                          column.id || column.key || colIndex
                        }`}
                        className="px-6 py-4 whitespace-nowrap text-sm text-gray-600"
                      >
                        {column.cell
                          ? column.cell({ row: { original: item } })
                          : column.render
                          ? column.render(item)
                          : item[column.accessorKey || column.key]}
                      </td>
                    ))}

                    {/* Actions column */}
                    {actions && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 text-right">
                        {typeof actions === "function"
                          ? actions(item)
                          : actions}
                      </td>
                    )}
                  </tr>
                ))
              : renderEmptyState()}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && totalPages > 1 && (
        <div className="bg-white px-6 py-4 flex items-center justify-between border-t border-gray-200">
          <div className="flex-1 flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-700">
                {totalItems > 0 ? (
                  <>
                    Showing{" "}
                    <span className="font-medium">
                      {Math.min(
                        (currentPage - 1) * itemsPerPage + 1,
                        totalItems
                      )}
                    </span>{" "}
                    to{" "}
                    <span className="font-medium">
                      {Math.min(currentPage * itemsPerPage, totalItems)}
                    </span>{" "}
                    of <span className="font-medium">{totalItems}</span> results
                  </>
                ) : (
                  <>No results found</>
                )}
              </p>
            </div>
            <div>
              <nav
                className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                aria-label="Pagination"
              >
                <button
                  onClick={() => paginate(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-3 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                    currentPage === 1
                      ? "text-gray-300 cursor-not-allowed"
                      : "text-gray-500 hover:bg-gray-50 transition-colors"
                  }`}
                >
                  <span className="sr-only">Previous</span>
                  <span className="material-icons-round text-sm">
                    chevron_left
                  </span>
                </button>

                {/* Page numbers */}
                {totalPages > 0 &&
                  Array.from({ length: Math.min(5, totalPages) }).map(
                    (_, idx) => {
                      // Calculate the page number to display
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = idx + 1;
                      } else if (currentPage <= 3) {
                        pageNum = idx + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + idx;
                      } else {
                        pageNum = currentPage - 2 + idx;
                      }

                      // Ensure pageNum is within valid range
                      if (pageNum < 1 || pageNum > totalPages) {
                        return null;
                      }

                      // Show ellipsis for large page ranges
                      if (
                        (pageNum === 2 && currentPage > 3) ||
                        (pageNum === totalPages - 1 &&
                          currentPage < totalPages - 2)
                      ) {
                        return (
                          <span
                            key={`ellipsis-${idx}`}
                            className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                          >
                            ...
                          </span>
                        );
                      }

                      // Regular page button
                      return (
                        <button
                          key={pageNum}
                          onClick={() => paginate(pageNum)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            currentPage === pageNum
                              ? "z-10 bg-indigo-50 border-indigo-500 text-indigo-600"
                              : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50 transition-colors"
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    }
                  )}

                <button
                  onClick={() => paginate(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`relative inline-flex items-center px-3 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                    currentPage === totalPages
                      ? "text-gray-300 cursor-not-allowed"
                      : "text-gray-500 hover:bg-gray-50 transition-colors"
                  }`}
                >
                  <span className="sr-only">Next</span>
                  <span className="material-icons-round text-sm">
                    chevron_right
                  </span>
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
