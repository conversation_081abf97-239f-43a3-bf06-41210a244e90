import React, { useState, useEffect } from 'react';
import DataTable from './DataTable';
import Modal from './Modal';
import LoadingSpinner from '../common/LoadingSpinner';
import ErrorAlert from '../common/ErrorAlert';
import SuccessAlert from '../common/SuccessAlert';

export default function CouponsManager() {
  // State for coupons data
  const [coupons, setCoupons] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  
  // State for modal and form
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('add'); // 'add' or 'edit'
  const [currentCoupon, setCurrentCoupon] = useState(null);
  const [formData, setFormData] = useState({
    code: '',
    type: 'percent',
    value: '',
    description: '',
    min_order_amount: '0',
    max_discount: '',
    is_active: true,
    start_date: '',
    end_date: '',
    usage_limit: '',
    user_limit: '1'
  });
  
  // State for delete confirmation
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [couponToDelete, setCouponToDelete] = useState(null);
  
  // Table columns configuration
  const columns = [
    { 
      key: 'code', 
      label: 'Coupon Code',
      render: (row) => (
        <span className="font-medium text-gray-900">{row.code}</span>
      )
    },
    { 
      key: 'type', 
      label: 'Type',
      render: (row) => {
        const typeLabels = {
          'percent': 'Percentage',
          'flat': 'Fixed Amount',
          'freeDelivery': 'Free Delivery'
        };
        return typeLabels[row.type] || row.type;
      }
    },
    { 
      key: 'value', 
      label: 'Value',
      render: (row) => {
        if (row.type === 'percent') {
          return `${row.value}%`;
        } else if (row.type === 'flat') {
          return `₹${row.value}`;
        } else {
          return '-';
        }
      }
    },
    { 
      key: 'min_order_amount', 
      label: 'Min. Order',
      render: (row) => row.min_order_amount > 0 ? `₹${row.min_order_amount}` : '-'
    },
    { 
      key: 'validity', 
      label: 'Validity',
      render: (row) => {
        if (!row.start_date && !row.end_date) {
          return 'No expiry';
        }
        
        const formatDate = (dateStr) => {
          if (!dateStr) return '';
          const date = new Date(dateStr);
          return date.toLocaleDateString('en-IN');
        };
        
        if (row.start_date && row.end_date) {
          return `${formatDate(row.start_date)} to ${formatDate(row.end_date)}`;
        } else if (row.end_date) {
          return `Until ${formatDate(row.end_date)}`;
        } else {
          return `From ${formatDate(row.start_date)}`;
        }
      }
    },
    { 
      key: 'is_active', 
      label: 'Status',
      render: (row) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          row.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
        }`}>
          {row.is_active ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (row) => (
        <div className="flex items-center space-x-3">
          <button
            onClick={() => handleEditCoupon(row)}
            className="text-blue-600 hover:text-blue-900"
            title="Edit"
          >
            <span className="material-icons-round text-sm">edit</span>
          </button>
          <button
            onClick={() => handleDeleteClick(row)}
            className="text-red-600 hover:text-red-900"
            title="Delete"
          >
            <span className="material-icons-round text-sm">delete</span>
          </button>
        </div>
      )
    }
  ];
  
  // Fetch coupons on component mount
  useEffect(() => {
    fetchCoupons();
  }, []);
  
  // Fetch coupons from API
  const fetchCoupons = async () => {
    setIsLoading(true);
    try {
      const response = await window.ApiClient.getAdminCoupons();
      if (response.success) {
        setCoupons(response.coupons || []);
      } else {
        setError(response.message || 'Failed to load coupons');
      }
    } catch (err) {
      console.error('Error fetching coupons:', err);
      setError('Failed to load coupons. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };
  
  // Open modal for adding a new coupon
  const handleAddCoupon = () => {
    setModalMode('add');
    setFormData({
      code: '',
      type: 'percent',
      value: '',
      description: '',
      min_order_amount: '0',
      max_discount: '',
      is_active: true,
      start_date: '',
      end_date: '',
      usage_limit: '',
      user_limit: '1'
    });
    setIsModalOpen(true);
  };
  
  // Open modal for editing an existing coupon
  const handleEditCoupon = (coupon) => {
    setModalMode('edit');
    setCurrentCoupon(coupon);
    
    // Format dates for input fields
    const formatDateForInput = (dateStr) => {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return date.toISOString().split('T')[0];
    };
    
    setFormData({
      code: coupon.code || '',
      type: coupon.type || 'percent',
      value: coupon.value?.toString() || '',
      description: coupon.description || '',
      min_order_amount: coupon.min_order_amount?.toString() || '0',
      max_discount: coupon.max_discount?.toString() || '',
      is_active: coupon.is_active !== false,
      start_date: formatDateForInput(coupon.start_date),
      end_date: formatDateForInput(coupon.end_date),
      usage_limit: coupon.usage_limit?.toString() || '',
      user_limit: coupon.user_limit?.toString() || '1'
    });
    
    setIsModalOpen(true);
  };
  
  // Handle delete button click
  const handleDeleteClick = (coupon) => {
    setCouponToDelete(coupon);
    setIsDeleteModalOpen(true);
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      // Prepare data for submission
      const couponData = {
        ...formData,
        value: parseFloat(formData.value),
        min_order_amount: parseFloat(formData.min_order_amount) || 0,
        max_discount: formData.max_discount ? parseFloat(formData.max_discount) : null,
        usage_limit: formData.usage_limit ? parseInt(formData.usage_limit) : null,
        user_limit: formData.user_limit ? parseInt(formData.user_limit) : 1
      };
      
      let response;
      
      if (modalMode === 'add') {
        // Create new coupon
        response = await window.ApiClient.createCoupon(couponData);
        if (response.success) {
          setSuccess('Coupon created successfully');
          fetchCoupons();
        }
      } else {
        // Update existing coupon
        response = await window.ApiClient.updateCoupon(currentCoupon.id, couponData);
        if (response.success) {
          setSuccess('Coupon updated successfully');
          fetchCoupons();
        }
      }
      
      setIsModalOpen(false);
    } catch (err) {
      console.error('Error saving coupon:', err);
      setError('Failed to save coupon: ' + (err.message || 'Unknown error'));
    }
  };
  
  // Handle coupon deletion
  const handleDeleteCoupon = async () => {
    if (!couponToDelete) return;
    
    try {
      const response = await window.ApiClient.deleteCoupon(couponToDelete.id);
      
      if (response.success) {
        setSuccess('Coupon deleted successfully');
        fetchCoupons();
      } else {
        setError(response.message || 'Failed to delete coupon');
      }
      
      setIsDeleteModalOpen(false);
      setCouponToDelete(null);
    } catch (err) {
      console.error('Error deleting coupon:', err);
      setError('Failed to delete coupon: ' + (err.message || 'Unknown error'));
    }
  };
  
  // Clear success message after 5 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        setSuccess(null);
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [success]);
  
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Discount Coupons</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage discount coupons for your customers
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <button
            onClick={handleAddCoupon}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            <span className="material-icons-round text-sm mr-1">add</span>
            Add Coupon
          </button>
        </div>
      </div>
      
      {/* Alerts */}
      {error && <ErrorAlert message={error} onClose={() => setError(null)} />}
      {success && <SuccessAlert message={success} onClose={() => setSuccess(null)} />}
      
      {/* Coupons Table */}
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        {isLoading ? (
          <div className="p-8 flex justify-center">
            <LoadingSpinner />
          </div>
        ) : (
          <DataTable
            columns={columns}
            data={coupons}
            pagination={true}
            searchable={true}
            emptyMessage="No coupons found"
          />
        )}
      </div>
      
      {/* Add/Edit Coupon Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={modalMode === 'add' ? 'Add New Coupon' : 'Edit Coupon'}
      >
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Coupon Code */}
            <div className="col-span-1">
              <label htmlFor="code" className="block text-sm font-medium text-gray-700">
                Coupon Code*
              </label>
              <input
                type="text"
                id="code"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                placeholder="e.g., SUMMER20"
                required
                readOnly={modalMode === 'edit'} // Coupon code cannot be changed once created
              />
              {modalMode === 'edit' && (
                <p className="mt-1 text-xs text-gray-500">Coupon code cannot be changed</p>
              )}
            </div>
            
            {/* Coupon Type */}
            <div className="col-span-1">
              <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                Discount Type*
              </label>
              <select
                id="type"
                name="type"
                value={formData.type}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                required
              >
                <option value="percent">Percentage Discount</option>
                <option value="flat">Fixed Amount Discount</option>
                <option value="freeDelivery">Free Delivery</option>
              </select>
            </div>
            
            {/* Discount Value */}
            {formData.type !== 'freeDelivery' && (
              <div className="col-span-1">
                <label htmlFor="value" className="block text-sm font-medium text-gray-700">
                  {formData.type === 'percent' ? 'Discount Percentage*' : 'Discount Amount (₹)*'}
                </label>
                <input
                  type="number"
                  id="value"
                  name="value"
                  value={formData.value}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                  placeholder={formData.type === 'percent' ? "e.g., 10" : "e.g., 100"}
                  min="0"
                  max={formData.type === 'percent' ? "100" : undefined}
                  step={formData.type === 'percent' ? "1" : "0.01"}
                  required
                />
              </div>
            )}
            
            {/* Max Discount (for percentage coupons) */}
            {formData.type === 'percent' && (
              <div className="col-span-1">
                <label htmlFor="max_discount" className="block text-sm font-medium text-gray-700">
                  Maximum Discount Amount (₹)
                </label>
                <input
                  type="number"
                  id="max_discount"
                  name="max_discount"
                  value={formData.max_discount}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                  placeholder="e.g., 500"
                  min="0"
                  step="0.01"
                />
                <p className="mt-1 text-xs text-gray-500">Leave empty for no maximum</p>
              </div>
            )}
            
            {/* Minimum Order Amount */}
            <div className="col-span-1">
              <label htmlFor="min_order_amount" className="block text-sm font-medium text-gray-700">
                Minimum Order Amount (₹)
              </label>
              <input
                type="number"
                id="min_order_amount"
                name="min_order_amount"
                value={formData.min_order_amount}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                placeholder="e.g., 500"
                min="0"
                step="0.01"
              />
            </div>
            
            {/* Description */}
            <div className="col-span-2">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description*
              </label>
              <input
                type="text"
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                placeholder="e.g., 10% off your order"
                required
              />
            </div>
            
            {/* Start Date */}
            <div className="col-span-1">
              <label htmlFor="start_date" className="block text-sm font-medium text-gray-700">
                Start Date
              </label>
              <input
                type="date"
                id="start_date"
                name="start_date"
                value={formData.start_date}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              />
            </div>
            
            {/* End Date */}
            <div className="col-span-1">
              <label htmlFor="end_date" className="block text-sm font-medium text-gray-700">
                End Date
              </label>
              <input
                type="date"
                id="end_date"
                name="end_date"
                value={formData.end_date}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              />
            </div>
            
            {/* Usage Limit */}
            <div className="col-span-1">
              <label htmlFor="usage_limit" className="block text-sm font-medium text-gray-700">
                Total Usage Limit
              </label>
              <input
                type="number"
                id="usage_limit"
                name="usage_limit"
                value={formData.usage_limit}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                placeholder="e.g., 100"
                min="0"
                step="1"
              />
              <p className="mt-1 text-xs text-gray-500">Leave empty for unlimited usage</p>
            </div>
            
            {/* User Limit */}
            <div className="col-span-1">
              <label htmlFor="user_limit" className="block text-sm font-medium text-gray-700">
                Usage Limit Per Customer
              </label>
              <input
                type="number"
                id="user_limit"
                name="user_limit"
                value={formData.user_limit}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                placeholder="e.g., 1"
                min="0"
                step="1"
              />
              <p className="mt-1 text-xs text-gray-500">Set to 0 for unlimited usage per customer</p>
            </div>
            
            {/* Active Status */}
            <div className="col-span-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_active"
                  name="is_active"
                  checked={formData.is_active}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                />
                <label htmlFor="is_active" className="ml-2 block text-sm text-gray-700">
                  Active
                </label>
              </div>
            </div>
          </div>
          
          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={() => setIsModalOpen(false)}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              {modalMode === 'add' ? 'Create Coupon' : 'Update Coupon'}
            </button>
          </div>
        </form>
      </Modal>
      
      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete Coupon"
        size="sm"
      >
        <div className="p-6">
          <p className="text-sm text-gray-500">
            Are you sure you want to delete the coupon <span className="font-medium text-gray-900">{couponToDelete?.code}</span>?
            This action cannot be undone.
          </p>
          
          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => setIsDeleteModalOpen(false)}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleDeleteCoupon}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Delete
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
