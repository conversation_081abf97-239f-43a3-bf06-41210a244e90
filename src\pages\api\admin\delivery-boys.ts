import type { APIRoute } from 'astro';
import { adminAuthMiddleware } from '../../../middleware/auth';
import {
  getDeliveryBoys,
  assignDeliveryBoyRole,
  removeDeliveryBoyRole,
  getDeliveryBoyLocations,
  assignLocationToDeliveryBoy,
  removeLocationFromDeliveryBoy
} from '../../../db/database';

export const prerender = false;

/**
 * Get all delivery boys (admin)
 */
export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get query parameters
    const url = new URL(request.url);
    const locationId = url.searchParams.get('locationId');

    // Get all delivery boys
    const deliveryBoys = await getDeliveryBoys(locals.runtime.env);

    // Always get the locations for each delivery boy
    // The 'all' parameter is a special case that ensures we get locations
    const deliveryBoysWithLocations = await Promise.all(
      deliveryBoys.map(async (deliveryBoy) => {
        const locations = await getDeliveryBoyLocations(locals.runtime.env, deliveryBoy.id);
        return {
          ...deliveryBoy,
          locations
        };
      })
    );

    return new Response(JSON.stringify({
      success: true,
      deliveryBoys: deliveryBoysWithLocations
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error fetching delivery boys:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch delivery boys' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

/**
 * Create a new delivery boy (assign role to existing user)
 */
export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get request data
    const data = await request.json();
    const { userId, locationIds } = data;

    if (!userId) {
      return new Response(JSON.stringify({ error: 'User ID is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Assign delivery boy role to the user
    const success = await assignDeliveryBoyRole(locals.runtime.env, userId);

    if (!success) {
      return new Response(JSON.stringify({ error: 'Failed to assign delivery boy role' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // If locationIds are provided, assign them to the delivery boy
    if (locationIds && Array.isArray(locationIds) && locationIds.length > 0) {
      await Promise.all(
        locationIds.map(locationId =>
          assignLocationToDeliveryBoy(locals.runtime.env, userId, locationId)
        )
      );
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Delivery boy role assigned successfully'
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error creating delivery boy:', error);
    return new Response(JSON.stringify({ error: 'Failed to create delivery boy' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

/**
 * Update a delivery boy (add/remove locations)
 */
export const PATCH: APIRoute = async ({ request, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get request data
    const data = await request.json();
    const { userId, action, locationId } = data;

    if (!userId || !action) {
      return new Response(JSON.stringify({ error: 'User ID and action are required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    let success = false;

    // Handle different actions
    switch (action) {
      case 'add_location':
        if (!locationId) {
          return new Response(JSON.stringify({ error: 'Location ID is required' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          });
        }
        const assignment = await assignLocationToDeliveryBoy(locals.runtime.env, userId, locationId);
        success = !!assignment;
        break;

      case 'remove_location':
        if (!locationId) {
          return new Response(JSON.stringify({ error: 'Location ID is required' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          });
        }
        success = await removeLocationFromDeliveryBoy(locals.runtime.env, userId, locationId);
        break;

      case 'remove_role':
        success = await removeDeliveryBoyRole(locals.runtime.env, userId);
        break;

      default:
        return new Response(JSON.stringify({ error: 'Invalid action' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
    }

    if (!success) {
      return new Response(JSON.stringify({ error: `Failed to ${action.replace('_', ' ')}` }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      message: `Successfully ${action.replace('_', ' ')}`
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error updating delivery boy:', error);
    return new Response(JSON.stringify({ error: 'Failed to update delivery boy' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
