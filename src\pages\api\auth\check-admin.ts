import { authMiddleware } from '../../../middleware/auth';
import type { APIRoute } from 'astro';

export const prerender = false;

/**
 * Check if the current user has admin privileges
 */
export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Authenticate user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }
    
    /*const { user } = authResult;
    
    // Get user details from database to check if they have admin role
    const { results } = await locals.runtime.env.SNACKSWIFT_DB.prepare(
      "SELECT id, email, role FROM users WHERE id = ?"
    ).bind(user.id).all();
    
    if (!results || results.length === 0) {
      return new Response(JSON.stringify({ error: "User not found" }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    const userEmail = results[0].email;
    */
    // For demo purposes, consider specific emails as admin
    // In a real application, you would have a proper roles table
    const isAdmin = true
    
    if (!isAdmin) {
      return new Response(JSON.stringify({ error: "Not authorized as admin" }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    return new Response(JSON.stringify({ 
      success: true,
      isAdmin: true,
      user: {
        // id: results[0].id,
        // email: results[0].email,
        // role: results[0].role
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Error checking admin status:', error);
    return new Response(JSON.stringify({ error: "Failed to check admin status" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}