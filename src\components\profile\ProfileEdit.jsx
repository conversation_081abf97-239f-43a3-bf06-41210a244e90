import React, { useState, useEffect } from "react";

export default function ProfileEdit() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  
  // Form state
  const [profile, setProfile] = useState({
    name: "",
    email: ""
  });
  
  // Form errors
  const [errors, setErrors] = useState({});

  // Load user profile on component mount
  useEffect(() => {
    fetchUserProfile();
  }, []);

  // Fetch user profile data from API
  const fetchUserProfile = async () => {
    setIsLoading(true);
    try {
      // Check if API client is available
      if (typeof window !== "undefined" && window.ApiClient) {
        // Real API call when implemented
        const userData = window.ApiClient.getCurrentUser();
        
        if (userData) {
          // Set form data from API response
          setProfile({
            name: userData.name || "",
            email: userData.email || ""
          });
        } else {
          throw new Error("User data not found");
        }
      }
    } catch (error) {
      console.error("Error fetching user profile:", error);
      showToast("Failed to load profile data", "error");
      
      // If authentication error, redirect to login
      if (error.message === 'Authentication required') {
        const currentPath = window.location.pathname;
        window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`;
      }
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    setProfile({
      ...profile,
      [name]: value
    });
    
    // Clear error when field is modified
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ""
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    if (!profile.name.trim()) newErrors.name = "Name is required";
    if (!profile.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(profile.email)) {
      newErrors.email = "Email is invalid";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setIsSaving(true);
    
    try {
      // Prepare user data for API
      const userData = {
        name: profile.name,
        email: profile.email
      };
      
      if (typeof window !== "undefined" && window.ApiClient) {
        // Real API call when implemented
        const result = await window.ApiClient.updateProfile(userData);
        
        if (result && result.success) {
          showToast("Profile updated successfully");
          
          // Update local storage with new profile data
          const currentUser = window.ApiClient.getCurrentUser();
          if (currentUser) {
            const updatedUser = { ...currentUser, ...userData };
            localStorage.setItem('user', JSON.stringify(updatedUser));
          }
          
          // Redirect back to profile page after short delay
          setTimeout(() => {
            window.location.href = "/profile";
          }, 1500);
        } else {
          throw new Error("Failed to update profile");
        }
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      showToast("Failed to update profile", "error");
    } finally {
      setIsSaving(false);
    }
  };
  
  // Show toast notification
  const showToast = (message, type = "success") => {
    const toast = document.createElement("div");
    toast.className = "fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-3 rounded-xl text-sm font-medium shadow-lg z-50 flex items-center opacity-0 transition-opacity duration-300";
    
    const icon = document.createElement("span");
    icon.className = "material-icons-round mr-2";
    
    if (type === "error") {
      icon.textContent = "error";
    } else if (type === "success") {
      icon.textContent = "check_circle";
    } else {
      icon.textContent = "info";
    }
    
    toast.appendChild(icon);
    
    const text = document.createElement("span");
    text.textContent = message;
    toast.appendChild(text);
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
      toast.style.opacity = "1";
    }, 10);
    
    setTimeout(() => {
      toast.style.opacity = "0";
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 300);
    }, 3000);
  };
  
  return (
    <div className="max-w-md mx-auto px-4 py-5">
      {/* Improved Loading State */}
      {isLoading ? (
        <div className="flex flex-col items-center justify-center py-16">
          <div className="w-14 h-14 border-4 border-gray-100 border-t-[#FF6B35] border-b-[#FF6B35] rounded-full animate-spin mb-5"></div>
          <p className="text-gray-600 font-medium">Loading your information...</p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Personal Information Section */}
          <div className="bg-white rounded-xl shadow-md p-6 border border-gray-100 relative overflow-hidden">
            {/* Decorative gradient background */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-orange-50 to-transparent rounded-bl-full -z-10"></div>
            
            <div className="flex items-center mb-5">
              <span className="material-icons-round text-[#FF6B35] mr-3 text-2xl">account_circle</span>
              <h2 className="text-xl font-semibold text-gray-800">Profile Information</h2>
            </div>
            
            <p className="text-gray-600 text-sm mb-6">
              Update your information to help us personalize your experience
            </p>
            
            {/* Full Name Field with improved styling */}
            <div className="mb-5">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Full Name
              </label>
              <div className={`relative rounded-xl overflow-hidden transition-all ${
                errors.name ? "ring-2 ring-red-500" : ""
              }`}>
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <span className="material-icons-round text-gray-400">person</span>
                </div>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={profile.name}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3.5 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6B35] transition-shadow"
                  placeholder="Enter your full name"
                />
              </div>
              {errors.name && (
                <p className="mt-2 text-red-600 text-sm flex items-center">
                  <span className="material-icons-round text-sm mr-1">error</span>
                  {errors.name}
                </p>
              )}
            </div>
            
            {/* Email Field with improved styling */}
            <div className="mb-1">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <div className={`relative rounded-xl overflow-hidden transition-all ${
                errors.email ? "ring-2 ring-red-500" : ""
              }`}>
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <span className="material-icons-round text-gray-400">email</span>
                </div>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={profile.email}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3.5 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#FF6B35] transition-shadow"
                  placeholder="Enter your email address"
                />
              </div>
              {errors.email && (
                <p className="mt-2 text-red-600 text-sm flex items-center">
                  <span className="material-icons-round text-sm mr-1">error</span>
                  {errors.email}
                </p>
              )}
            </div>
          </div>
          
          {/* Improved Form Actions */}
          <div className="flex gap-4 mb-24">
            <a
              href="/profile"
              className="flex-1 py-3.5 border border-gray-200 rounded-xl font-medium text-gray-700 hover:bg-gray-50 transition-all shadow-sm hover:shadow-none flex items-center justify-center"
            >
              <span className="material-icons-round mr-2 text-gray-500">arrow_back</span>
              Cancel
            </a>
            <button
              type="submit"
              disabled={isSaving}
              className="flex-1 py-3.5 bg-[#FF6B35] text-white rounded-xl font-medium hover:bg-[#e55c28] transition-all shadow-sm hover:shadow flex items-center justify-center"
            >
              {isSaving ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              ) : (
                <span className="material-icons-round mr-2">save</span>
              )}
              {isSaving ? "Saving..." : "Save Changes"}
            </button>
          </div>
        </form>
      )}
    </div>
  );
}
