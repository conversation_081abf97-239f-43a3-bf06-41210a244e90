// @ts-check
import { defineConfig } from "astro/config";

import cloudflare from "@astrojs/cloudflare";

import tailwindcss from "@tailwindcss/vite";

import react from "@astrojs/react";

import sitemap from "@astrojs/sitemap";

import playformCompress from "@playform/compress";

// https://astro.build/config
export default defineConfig({
  site: "https://sreekarpublishers.com",
  adapter: cloudflare({
    platformProxy: {
      enabled: true,
    },
  }),

  vite: {
    plugins: [tailwindcss()],
    resolve: {
      // Use react-dom/server.edge instead of react-dom/server.browser for React 19.
      // Without this, MessageChannel from node:worker_threads needs to be polyfilled.
      alias: import.meta.env.PROD && {
        "react-dom/server": "react-dom/server.edge",
      },
    },
  },

  // Disable view transitions for simpler application
  output: 'server', // Use server output for dynamic content
  prefetch: false,
  // View transitions disabled
  viewTransitions: false,

  integrations: [react(), sitemap({
    filter: (page) => !page.includes("/admin/"), // Exclude admin pages from sitemap
  }), playformCompress()],
});