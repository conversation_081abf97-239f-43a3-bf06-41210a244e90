import { adminAuthMiddleware } from '../../../middleware/auth';
import { 
  getAllCoupons, 
  createCoupon, 
  getCouponById 
} from '../../../db/database';

export const prerender = false;

/**
 * Get all coupons (admin)
 */
export async function GET({ request, locals }) {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get all coupons
    const coupons = await getAllCoupons(locals.runtime.env);

    return new Response(JSON.stringify({
      success: true,
      coupons
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error fetching coupons:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to fetch coupons',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Create a new coupon (admin)
 */
export async function POST({ request, locals }) {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Parse request body
    const data = await request.json();

    // Validate required fields
    if (!data.code || !data.type || (data.type !== 'freeDelivery' && !data.value) || !data.description) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Required fields are missing'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if coupon code already exists
    const existingCoupon = await getCouponById(locals.runtime.env, data.code);
    if (existingCoupon) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Coupon code already exists'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Create coupon
    const coupon = await createCoupon(locals.runtime.env, {
      code: data.code.toUpperCase(),
      type: data.type,
      value: data.type === 'freeDelivery' ? 0 : parseFloat(data.value),
      description: data.description,
      min_order_amount: data.min_order_amount ? parseFloat(data.min_order_amount) : 0,
      max_discount: data.max_discount ? parseFloat(data.max_discount) : null,
      is_active: data.is_active !== false,
      start_date: data.start_date || null,
      end_date: data.end_date || null,
      usage_limit: data.usage_limit ? parseInt(data.usage_limit) : null,
      user_limit: data.user_limit ? parseInt(data.user_limit) : 1
    });

    if (!coupon) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Failed to create coupon'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      coupon
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error creating coupon:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to create coupon',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
