import type { APIRoute } from 'astro';
export const prerender = false;

// Get a single category by ID
export const GET: APIRoute = async ({ params, locals }) => {
  // Check admin authentication
  // In a production app, uncomment this code
  /*
  if (!locals.user || !locals.user.isAdmin) {
    return new Response(JSON.stringify({
      error: 'Unauthorized'
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
  */

  try {
    const categoryId = params.id;

    if (!categoryId) {
      return new Response(JSON.stringify({
        error: 'Category ID is required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    const env = locals.runtime.env;

    // Get the category from the database
    const { results } = await env.SNACKSWIFT_DB.prepare(`
      SELECT * FROM categories WHERE id = ?
    `).bind(categoryId).all();

    if (!results || results.length === 0) {
      return new Response(JSON.stringify({
        error: 'Category not found'
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    return new Response(JSON.stringify({
      category: results[0]
    }), {
      headers: {
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('Error fetching category:', error);
    return new Response(JSON.stringify({
      error: 'Failed to fetch category',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Update a category
export const PUT: APIRoute = async ({ request, params, locals }) => {
  // Check admin authentication
  // In a production app, uncomment this code
  /*
  if (!locals.user || !locals.user.isAdmin) {
    return new Response(JSON.stringify({
      error: 'Unauthorized'
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
  */

  try {
    const categoryId = params.id;

    if (!categoryId) {
      return new Response(JSON.stringify({
        error: 'Category ID is required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    const categoryData = await request.json() as { name?: string; icon?: string; color?: string };
    const env = locals.runtime.env;

    // Ensure the category exists
    const { results: existingCategory } = await env.SNACKSWIFT_DB.prepare(`
      SELECT * FROM categories WHERE id = ?
    `).bind(categoryId).all();

    if (!existingCategory || existingCategory.length === 0) {
      return new Response(JSON.stringify({
        error: 'Category not found'
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Validate required fields
    if (!categoryData.name) {
      return new Response(JSON.stringify({
        error: `Missing required field: name`
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Get the existing category data to preserve icon and color
    const existingCategoryData = existingCategory[0] as { icon: string; color: string };

    // Update the category in the database
    const result = await env.SNACKSWIFT_DB.prepare(`
      UPDATE categories SET
        name = ?,
        icon = ?,
        color = ?
      WHERE id = ?
    `).bind(
      categoryData.name,
      categoryData.icon || existingCategoryData.icon || '📁',
      categoryData.color || existingCategoryData.color || '#808080',
      categoryId
    ).run();

    if (!result || !result.success) {
      throw new Error('Failed to update category');
    }

    // Retrieve the updated category
    const { results: updatedCategory } = await env.SNACKSWIFT_DB.prepare(`
      SELECT * FROM categories WHERE id = ?
    `).bind(categoryId).all();

    return new Response(JSON.stringify({
      success: true,
      category: updatedCategory[0],
      message: 'Category updated successfully'
    }), {
      headers: {
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('Error updating category:', error);
    return new Response(JSON.stringify({
      error: 'Failed to update category',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Delete a category
export const DELETE: APIRoute = async ({ params, locals }) => {
  // Check admin authentication
  // In a production app, uncomment this code
  /*
  if (!locals.user || !locals.user.isAdmin) {
    return new Response(JSON.stringify({
      error: 'Unauthorized'
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
  */

  try {
    const categoryId = params.id;

    if (!categoryId) {
      return new Response(JSON.stringify({
        error: 'Category ID is required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    const env = locals.runtime.env;

    // Check if the category exists
    const { results: existingCategory } = await env.SNACKSWIFT_DB.prepare(`
      SELECT * FROM categories WHERE id = ?
    `).bind(categoryId).all();

    if (!existingCategory || existingCategory.length === 0) {
      return new Response(JSON.stringify({
        error: 'Category not found'
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Check if there are products using this category
    const { results: productsWithCategory } = await env.SNACKSWIFT_DB.prepare(`
      SELECT COUNT(*) as count FROM products WHERE category_id = ?
    `).bind(categoryId).all();

    if (productsWithCategory && productsWithCategory[0] && productsWithCategory[0].count > 0) {
      return new Response(JSON.stringify({
        error: 'Cannot delete category because it is used by products',
        details: 'Please reassign or delete the products in this category first.'
      }), {
        status: 409, // Conflict
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Delete the category
    const result = await env.SNACKSWIFT_DB.prepare(`
      DELETE FROM categories WHERE id = ?
    `).bind(categoryId).run();

    if (!result || !result.success) {
      throw new Error('Failed to delete category');
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Category deleted successfully'
    }), {
      headers: {
        'Content-Type': 'application/json'
      }
    });

  } catch (error) {
    console.error('Error deleting category:', error);
    return new Response(JSON.stringify({
      error: 'Failed to delete category',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
