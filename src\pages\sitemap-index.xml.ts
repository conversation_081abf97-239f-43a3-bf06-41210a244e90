import type { APIRoute } from 'astro';
import { getProducts, getCategories } from '../db/database';

export const GET: APIRoute = async ({ locals }) => {
  try {
    const siteUrl = 'https://sreekarpublishers.com';
    const env = locals.runtime.env;
    const today = new Date().toISOString().split('T')[0];

    // Basic sitemap structure with our main sitemap
    let sitemapEntries = [`
  <sitemap>
    <loc>${siteUrl}/sitemap-0.xml</loc>
    <lastmod>${today}</lastmod>
  </sitemap>`];

    // Check if we need additional sitemaps based on content size
    try {
      // Get total product count to decide if we need separate product sitemaps
      const products = await getProducts(env, { limit: 10 });
      const productCount = products.length > 0 ? 1000 : 0; // Estimate total count

      // If we have many products, create a dedicated product sitemap
      if (productCount > 500) {
        sitemapEntries.push(`
  <sitemap>
    <loc>${siteUrl}/sitemap-products.xml</loc>
    <lastmod>${today}</lastmod>
  </sitemap>`);
      }

      // Get categories to decide if we need separate category sitemaps
      const categories = await getCategories(env);

      // If we have many categories with many filtering options
      if (categories.length > 20) {
        sitemapEntries.push(`
  <sitemap>
    <loc>${siteUrl}/sitemap-categories.xml</loc>
    <lastmod>${today}</lastmod>
  </sitemap>`);
      }
    } catch (error) {
      console.error('Error checking content size for sitemaps:', error);
    }

    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapEntries.join('')}
</sitemapindex>`;

    return new Response(xml, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml'
      }
    });
  } catch (error) {
    console.error('Error serving sitemap-index.xml:', error);
    return new Response('Error generating sitemap', {
      status: 500,
      headers: {
        'Content-Type': 'text/plain'
      }
    });
  }
}