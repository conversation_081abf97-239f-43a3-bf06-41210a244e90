import { generateOTP } from '../../../db/database';

export const prerender = false;

export async function POST({ request, locals }) {
  try {
    if (!locals.runtime || !locals.runtime.env) {
      return new Response(JSON.stringify({ error: "Runtime environment not available" }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    const { phoneNumber } = await request.json();
    
    if (!phoneNumber || phoneNumber.length < 8) {
      return new Response(JSON.stringify({ error: "Valid phone number is required" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Generate OTP
    const otp = await generateOTP(locals.runtime.env, phoneNumber);
    
    if (!otp) {
      return new Response(JSON.stringify({ error: "Failed to generate OTP" }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // In a real production app, you would send the OTP via SMS using a service like Twilio
    // For this demo, we'll just return the OTP in the response (for testing purposes only)
    console.log(`OTP for ${phoneNumber}: ${otp}`);
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: "OTP sent successfully",
      // Only include OTP in development mode
      otp
    //   ...(process.env.NODE_ENV === 'development' ? { otp } : {})
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Error in send-otp API:', error);
    return new Response(JSON.stringify({ error: "Failed to process request" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
