import type { APIRoute } from 'astro';
import { getOrderById, cancelOrder, updateOrderStatus } from '../../../db/database';
import { authMiddleware } from '../../../middleware/auth';

export const prerender = false;

/**
 * Get a specific order by ID
 */
export const GET: APIRoute = async ({ request, params, locals }) => {
  try {
    // Authenticate user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }
    
    const { user } = authResult;
    
    // Get order ID from URL params
    const orderId = parseInt(params.id);
    
    if (isNaN(orderId)) {
      return new Response(JSON.stringify({ error: "Invalid order ID" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Get the order
    const order = await getOrderById(locals.runtime.env, orderId, user.id);
    
    if (!order) {
      return new Response(JSON.stringify({ error: "Order not found" }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    return new Response(JSON.stringify({ order }), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'private, max-age=10' // Short cache for better performance
      }
    });
    
  } catch (error) {
    console.error('Error retrieving order:', error);
    return new Response(JSON.stringify({ error: "Failed to retrieve order" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

/**
 * Update an order (cancel an order or change status)
 */
export const PATCH: APIRoute = async ({ request, params, locals }) => {
  try {
    // Authenticate user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }
    
    const { user } = authResult;
    
    // Get order ID from URL params
    const orderId = parseInt(params.id);
    
    if (isNaN(orderId)) {
      return new Response(JSON.stringify({ error: "Invalid order ID" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Get request data
    const { action, reason, status } = await request.json();
    
    // Handle based on action
    if (action === 'cancel') {
      if (!reason || typeof reason !== 'string' || reason.trim() === '') {
        return new Response(JSON.stringify({ error: "Cancellation reason is required" }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      const success = await cancelOrder(locals.runtime.env, orderId, user.id, reason);
      
      if (!success) {
        return new Response(JSON.stringify({ error: "Failed to cancel order or order cannot be cancelled" }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      return new Response(JSON.stringify({ 
        success: true,
        message: "Order cancelled successfully" 
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } 
    // For admin operations only (to be implemented with admin authentication)
    else if (action === 'update_status') {
      // This would be for admin operations, not implemented in current version
      return new Response(JSON.stringify({ error: "Not authorized to perform this action" }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    } 
    else {
      return new Response(JSON.stringify({ error: "Invalid action" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
  } catch (error) {
    console.error('Error updating order:', error);
    return new Response(JSON.stringify({ error: "Failed to update order" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};