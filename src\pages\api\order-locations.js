import { getOrderLocations } from '../../db/database';

export const prerender = false;

/**
 * Get available order locations
 * 
 * This endpoint returns a list of available order locations
 * that customers can select for delivery.
 */
export async function GET({ request, locals }) {
  try {
    // Get all active order locations
    const locations = await getOrderLocations(locals.runtime.env, { activeOnly: true });
    
    return new Response(JSON.stringify({
      success: true,
      locations
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error getting order locations:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to get order locations',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
