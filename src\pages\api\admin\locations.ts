import type { APIRoute } from 'astro';
import { adminAuthMiddleware } from '../../../middleware/auth';
import { getAdminOrderLocations, addOrderLocation, updateOrderLocation, deleteOrderLocation } from '../../../db/database';

export const prerender = false;

/**
 * Get all order locations (admin)
 */
export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    try {
      // Get all locations
      const locations = await getAdminOrderLocations(locals.runtime.env);

      return new Response(JSON.stringify({
        success: true,
        locations
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (dbError) {
      // Check if the error is because the table doesn't exist
      const errorMessage = dbError.toString();
      if (errorMessage.includes('no such table')) {
        return new Response(JSON.stringify({
          success: false,
          error: errorMessage,
          message: 'The locations table does not exist yet. It will be created when you add your first location.'
        }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Re-throw for other errors
      throw dbError;
    }
  } catch (error) {
    console.error('Error fetching locations:', error);

    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to fetch locations',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

/**
 * Create a new order location (admin)
 */
export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Parse request body
    const data = await request.json();

    // Validate required fields
    if (!data.name || !data.address) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Name and address are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    try {
      // Create location
      const location = await addOrderLocation(locals.runtime.env, {
        name: data.name,
        address: data.address,
        is_active: data.is_active !== false // Default to active if not specified
      });

      if (!location) {
        return new Response(JSON.stringify({
          success: false,
          message: 'Failed to create location'
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      return new Response(JSON.stringify({
        success: true,
        location
      }), {
        status: 201,
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (dbError) {
      // Check if the error is because the table doesn't exist
      const errorMessage = dbError.toString();
      if (errorMessage.includes('no such table')) {
        try {
          // Create the table
          await locals.runtime.env.SNACKSWIFT_DB.prepare(`
            CREATE TABLE IF NOT EXISTS order_locations (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT NOT NULL,
              address TEXT NOT NULL,
              is_active BOOLEAN DEFAULT 1,
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

            CREATE INDEX IF NOT EXISTS idx_order_locations_is_active ON order_locations(is_active);
          `).run();

          // Add location_id field to orders table if it doesn't exist
          try {
            await locals.runtime.env.SNACKSWIFT_DB.prepare(`
              ALTER TABLE orders ADD COLUMN location_id INTEGER;
            `).run();
          } catch (alterError) {
            // Ignore error if column already exists
            console.log('Note: location_id column might already exist in orders table');
          }

          // Try creating the location again
          const location = await addOrderLocation(locals.runtime.env, {
            name: data.name,
            address: data.address,
            is_active: data.is_active !== false
          });

          if (!location) {
            throw new Error('Failed to create location after creating table');
          }

          return new Response(JSON.stringify({
            success: true,
            location,
            message: 'Created locations table and added first location'
          }), {
            status: 201,
            headers: { 'Content-Type': 'application/json' }
          });
        } catch (createTableError) {
          console.error('Error creating locations table:', createTableError);
          return new Response(JSON.stringify({
            success: false,
            message: 'Failed to create locations table',
            error: createTableError.toString()
          }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          });
        }
      }

      // Re-throw for other errors
      throw dbError;
    }
  } catch (error) {
    console.error('Error creating location:', error);

    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to create location',
      error: error.toString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

/**
 * Update or delete multiple locations (admin)
 */
export const PATCH: APIRoute = async ({ request, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Parse request body
    const data = await request.json();

    // Validate action
    if (!data.action) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Action is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Handle bulk actions
    if (data.action === 'bulk_delete' && Array.isArray(data.locationIds)) {
      const results = [];

      for (const locationId of data.locationIds) {
        const result = await deleteOrderLocation(locals.runtime.env, locationId);
        results.push({ id: locationId, success: result });
      }

      return new Response(JSON.stringify({
        success: true,
        results
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: false,
      message: 'Invalid action'
    }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error updating locations:', error);

    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to update locations'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
