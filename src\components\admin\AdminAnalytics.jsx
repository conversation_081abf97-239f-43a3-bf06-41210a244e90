import React, { useState, useEffect } from 'react';

export default function AdminAnalytics() {
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('week');
  const [dataType, setDataType] = useState('revenue');
  const [analyticsData, setAnalyticsData] = useState(null);
  
  // Fetch analytics data on component mount and when filters change
  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange, dataType]);

  // Mock data function - in a real app this would call an API
  const fetchAnalyticsData = async () => {
    setIsLoading(true);
    
    // Simulate API request delay
    setTimeout(() => {
      // Generate random chart data based on selected time range and data type
      const dates = generateDateRange(timeRange);
      
      // Revenue data
      const revenueData = dates.map(date => ({
        date,
        value: Math.floor(Math.random() * 1000) + 500
      }));
      
      // Orders data
      const ordersData = dates.map(date => ({
        date,
        value: Math.floor(Math.random() * 50) + 10
      }));
      
      // Users data
      const usersData = dates.map(date => ({
        date,
        value: Math.floor(Math.random() * 20) + 5
      }));
      
      // Popular items data
      const popularItems = [
        { name: 'Burger Deluxe', sold: Math.floor(Math.random() * 100) + 50 },
        { name: 'Pizza Margherita', sold: Math.floor(Math.random() * 100) + 40 },
        { name: 'Chicken Wings', sold: Math.floor(Math.random() * 100) + 30 },
        { name: 'Veggie Wrap', sold: Math.floor(Math.random() * 100) + 20 },
        { name: 'Chocolate Shake', sold: Math.floor(Math.random() * 100) + 10 }
      ].sort((a, b) => b.sold - a.sold);
      
      // Regional data
      const regionalData = [
        { region: 'North', value: Math.floor(Math.random() * 1000) + 500 },
        { region: 'South', value: Math.floor(Math.random() * 1000) + 500 },
        { region: 'East', value: Math.floor(Math.random() * 1000) + 500 },
        { region: 'West', value: Math.floor(Math.random() * 1000) + 500 },
        { region: 'Central', value: Math.floor(Math.random() * 1000) + 500 }
      ].sort((a, b) => b.value - a.value);
      
      setAnalyticsData({
        revenue: revenueData,
        orders: ordersData,
        users: usersData,
        popularItems,
        regionalData
      });
      
      setIsLoading(false);
    }, 800);
  };

  // Generate a range of dates based on the selected time range
  const generateDateRange = (range) => {
    const dates = [];
    const now = new Date();
    
    let daysToGenerate = 7; // Default to week
    
    if (range === 'month') {
      daysToGenerate = 30;
    } else if (range === 'year') {
      daysToGenerate = 12; // For year, we'll use months instead of days
    }
    
    for (let i = 0; i < daysToGenerate; i++) {
      if (range === 'year') {
        // For year view, generate months
        const month = new Date(now.getFullYear(), i, 1);
        dates.push(formatDate(month, 'month'));
      } else {
        // For week or month view, generate days
        const date = new Date(now);
        date.setDate(now.getDate() - (daysToGenerate - i - 1));
        dates.push(formatDate(date, 'day'));
      }
    }
    
    return dates;
  };

  // Format date based on the time range
  const formatDate = (date, format) => {
    if (format === 'month') {
      return date.toLocaleDateString('en-US', { month: 'short' });
    }
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  // Format currency
  const formatCurrency = (value) => {
    return `$${value.toFixed(2)}`;
  };

  // Get total from chart data
  const getTotal = (data) => {
    if (!data || !data.length) return 0;
    return data.reduce((sum, item) => sum + item.value, 0);
  };

  // Get percentage change compared to previous period
  const getPercentageChange = () => {
    // In a real implementation, this would calculate the percentage change
    // between the current period and the previous period
    return Math.floor(Math.random() * 20) - 5; // Random value between -5 and 15
  };

  // Render chart based on data type
  const renderChart = () => {
    if (isLoading || !analyticsData) {
      return (
        <div className="h-64 flex items-center justify-center">
          <div className="animate-pulse flex space-x-4">
            <div className="flex-1 space-y-6">
              <div className="h-40 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      );
    }

    const data = analyticsData[dataType];
    const maxValue = Math.max(...data.map(item => item.value));
    
    return (
      <div className="h-64">
        <div className="h-full w-full flex items-end space-x-2 pb-6 pt-10 px-2">
          {data.map((item, index) => {
            const heightPercentage = (item.value / maxValue) * 100;
            
            return (
              <div key={index} className="flex-1 flex flex-col justify-end items-center">
                <div 
                  className="w-full bg-orange-500 rounded-t-md relative group cursor-pointer hover:bg-orange-600 transition-colors"
                  style={{ height: `${heightPercentage}%` }}
                >
                  {/* Tooltip */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    {dataType === 'revenue' ? formatCurrency(item.value) : item.value}
                  </div>
                </div>
                <span className="text-xs mt-2 text-gray-500">{item.date}</span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
        <h1 className="text-2xl font-semibold text-gray-800">Analytics</h1>
        
        <div className="flex space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="block w-40 pl-3 pr-10 py-2 text-base border border-gray-300 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
          >
            <option value="week">Last 7 days</option>
            <option value="month">Last 30 days</option>
            <option value="year">Last 12 months</option>
          </select>
          
          <button
            className="flex items-center justify-center px-4 py-2 bg-orange-500 text-white rounded-lg shadow-sm hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
          >
            <span className="material-icons-round mr-2 text-sm">file_download</span>
            Export
          </button>
        </div>
      </div>
      
      {/* Stats Overview */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Revenue */}
        <div 
          className={`bg-white rounded-xl shadow-sm p-6 border border-gray-200 cursor-pointer transition-all duration-200 ${dataType === 'revenue' ? 'ring-2 ring-orange-500' : 'hover:bg-gray-50'}`}
          onClick={() => setDataType('revenue')}
        >
          {isLoading ? (
            <div className="animate-pulse space-y-3">
              <div className="h-4 bg-gray-200 rounded w-1/3"></div>
              <div className="h-8 bg-gray-200 rounded w-2/3"></div>
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-500">Total Revenue</h3>
                <span className="material-icons-round text-green-500 bg-green-50 rounded-full p-2">
                  attach_money
                </span>
              </div>
              <p className="mt-2 text-3xl font-bold text-gray-900">
                {formatCurrency(getTotal(analyticsData?.revenue))}
              </p>
              <div className="mt-2 flex items-center">
                {getPercentageChange() > 0 ? (
                  <span className="text-sm text-green-600 flex items-center">
                    <span className="material-icons-round text-sm mr-1">arrow_upward</span>
                    {Math.abs(getPercentageChange())}%
                  </span>
                ) : (
                  <span className="text-sm text-red-600 flex items-center">
                    <span className="material-icons-round text-sm mr-1">arrow_downward</span>
                    {Math.abs(getPercentageChange())}%
                  </span>
                )}
                <span className="text-gray-500 ml-1 text-sm">vs last period</span>
              </div>
            </>
          )}
        </div>
        
        {/* Orders */}
        <div 
          className={`bg-white rounded-xl shadow-sm p-6 border border-gray-200 cursor-pointer transition-all duration-200 ${dataType === 'orders' ? 'ring-2 ring-orange-500' : 'hover:bg-gray-50'}`}
          onClick={() => setDataType('orders')}
        >
          {isLoading ? (
            <div className="animate-pulse space-y-3">
              <div className="h-4 bg-gray-200 rounded w-1/3"></div>
              <div className="h-8 bg-gray-200 rounded w-2/3"></div>
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-500">Total Orders</h3>
                <span className="material-icons-round text-blue-500 bg-blue-50 rounded-full p-2">
                  shopping_bag
                </span>
              </div>
              <p className="mt-2 text-3xl font-bold text-gray-900">
                {getTotal(analyticsData?.orders)}
              </p>
              <div className="mt-2 flex items-center">
                {getPercentageChange() > 0 ? (
                  <span className="text-sm text-green-600 flex items-center">
                    <span className="material-icons-round text-sm mr-1">arrow_upward</span>
                    {Math.abs(getPercentageChange())}%
                  </span>
                ) : (
                  <span className="text-sm text-red-600 flex items-center">
                    <span className="material-icons-round text-sm mr-1">arrow_downward</span>
                    {Math.abs(getPercentageChange())}%
                  </span>
                )}
                <span className="text-gray-500 ml-1 text-sm">vs last period</span>
              </div>
            </>
          )}
        </div>
        
        {/* Users */}
        <div 
          className={`bg-white rounded-xl shadow-sm p-6 border border-gray-200 cursor-pointer transition-all duration-200 ${dataType === 'users' ? 'ring-2 ring-orange-500' : 'hover:bg-gray-50'}`}
          onClick={() => setDataType('users')}
        >
          {isLoading ? (
            <div className="animate-pulse space-y-3">
              <div className="h-4 bg-gray-200 rounded w-1/3"></div>
              <div className="h-8 bg-gray-200 rounded w-2/3"></div>
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-500">New Users</h3>
                <span className="material-icons-round text-purple-500 bg-purple-50 rounded-full p-2">
                  person_add
                </span>
              </div>
              <p className="mt-2 text-3xl font-bold text-gray-900">
                {getTotal(analyticsData?.users)}
              </p>
              <div className="mt-2 flex items-center">
                {getPercentageChange() > 0 ? (
                  <span className="text-sm text-green-600 flex items-center">
                    <span className="material-icons-round text-sm mr-1">arrow_upward</span>
                    {Math.abs(getPercentageChange())}%
                  </span>
                ) : (
                  <span className="text-sm text-red-600 flex items-center">
                    <span className="material-icons-round text-sm mr-1">arrow_downward</span>
                    {Math.abs(getPercentageChange())}%
                  </span>
                )}
                <span className="text-gray-500 ml-1 text-sm">vs last period</span>
              </div>
            </>
          )}
        </div>
        
        {/* Average Order Value */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200 hover:bg-gray-50">
          {isLoading ? (
            <div className="animate-pulse space-y-3">
              <div className="h-4 bg-gray-200 rounded w-1/3"></div>
              <div className="h-8 bg-gray-200 rounded w-2/3"></div>
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-500">Avg. Order Value</h3>
                <span className="material-icons-round text-orange-500 bg-orange-50 rounded-full p-2">
                  payments
                </span>
              </div>
              <p className="mt-2 text-3xl font-bold text-gray-900">
                {formatCurrency(getTotal(analyticsData?.revenue) / getTotal(analyticsData?.orders) || 0)}
              </p>
              <div className="mt-2 flex items-center">
                {getPercentageChange() > 0 ? (
                  <span className="text-sm text-green-600 flex items-center">
                    <span className="material-icons-round text-sm mr-1">arrow_upward</span>
                    {Math.abs(getPercentageChange())}%
                  </span>
                ) : (
                  <span className="text-sm text-red-600 flex items-center">
                    <span className="material-icons-round text-sm mr-1">arrow_downward</span>
                    {Math.abs(getPercentageChange())}%
                  </span>
                )}
                <span className="text-gray-500 ml-1 text-sm">vs last period</span>
              </div>
            </>
          )}
        </div>
      </div>
      
      {/* Main Chart */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-800">
            {dataType === 'revenue' ? 'Revenue' : dataType === 'orders' ? 'Orders' : 'New Users'} Overview
          </h3>
          
          <div className="flex space-x-2">
            <div 
              className={`px-3 py-1 text-sm cursor-pointer rounded-md ${dataType === 'revenue' ? 'bg-orange-100 text-orange-700' : 'hover:bg-gray-100'}`}
              onClick={() => setDataType('revenue')}
            >
              Revenue
            </div>
            <div 
              className={`px-3 py-1 text-sm cursor-pointer rounded-md ${dataType === 'orders' ? 'bg-orange-100 text-orange-700' : 'hover:bg-gray-100'}`}
              onClick={() => setDataType('orders')}
            >
              Orders
            </div>
            <div 
              className={`px-3 py-1 text-sm cursor-pointer rounded-md ${dataType === 'users' ? 'bg-orange-100 text-orange-700' : 'hover:bg-gray-100'}`}
              onClick={() => setDataType('users')}
            >
              Users
            </div>
          </div>
        </div>
        
        {renderChart()}
      </div>
      
      {/* Additional Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Products */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-800">Top Products</h3>
          </div>
          <div className="p-6">
            {isLoading ? (
              <div className="animate-pulse space-y-4">
                {Array(5).fill().map((_, i) => (
                  <div key={i} className="flex items-center space-x-3">
                    <div className="h-8 w-8 bg-gray-200 rounded-md"></div>
                    <div className="flex-1 h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 w-12 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-3">
                {analyticsData?.popularItems.map((item, index) => (
                  <div key={index} className="flex items-center">
                    <div className="w-6 h-6 text-gray-500 flex items-center justify-center font-semibold">
                      {index + 1}
                    </div>
                    <div className="ml-4 flex-1">
                      <h4 className="text-sm font-medium text-gray-900">{item.name}</h4>
                    </div>
                    <div className="ml-auto font-medium text-sm text-gray-900">
                      {item.sold} sold
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        {/* Regional Performance */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-800">Regional Performance</h3>
          </div>
          <div className="p-6">
            {isLoading ? (
              <div className="animate-pulse space-y-4">
                {Array(5).fill().map((_, i) => (
                  <div key={i} className="space-y-2">
                    <div className="flex justify-between">
                      <div className="h-4 w-20 bg-gray-200 rounded"></div>
                      <div className="h-4 w-12 bg-gray-200 rounded"></div>
                    </div>
                    <div className="h-3 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {analyticsData?.regionalData.map((item, index) => {
                  // Calculate percentage of max value for progress bar
                  const maxValue = Math.max(...analyticsData.regionalData.map(r => r.value));
                  const percentage = (item.value / maxValue) * 100;
                  
                  return (
                    <div key={index}>
                      <div className="flex justify-between text-sm">
                        <span className="font-medium text-gray-700">{item.region}</span>
                        <span className="text-gray-500">{formatCurrency(item.value)}</span>
                      </div>
                      <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-orange-500 h-2 rounded-full" 
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}