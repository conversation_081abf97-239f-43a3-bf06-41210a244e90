import { useState, useEffect } from 'react';

export function PhoneLogin({ onLoginSuccess }) {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otpCode, setOtpCode] = useState('');
  const [step, setStep] = useState('phone'); // 'phone', 'otp', 'loading'
  const [error, setError] = useState('');
  const [timer, setTimer] = useState(0);
  const [isResending, setIsResending] = useState(false);
  const [redirectUrl, setRedirectUrl] = useState('/');

  // Extract redirect URL from query parameters on component mount
  useEffect(() => {
    if (typeof window !== "undefined") {
      const params = new URLSearchParams(window.location.search);
      const redirect = params.get('redirect');
      if (redirect) {
        setRedirectUrl(redirect);
      }
    }
  }, []);

  const validatePhoneNumber = () => {
    // Ensure phone number is exactly 10 digits
    return /^[4-9]\d{9}$/.test(phoneNumber.trim());
  };
  
  // Handle phone number input with restrictions
  const handlePhoneChange = (e) => {
    const value = e.target.value;
    
    // Only allow digits and limit to 10 characters
    const sanitizedValue = value.replace(/\D/g, '').slice(0, 10);
    
    setPhoneNumber(sanitizedValue);
    
    if (error && sanitizedValue.length === 10) {
      setError('');
    }
  };
  
  const handleSendOTP = async (isResend = false) => {
    if (!validatePhoneNumber()) {
      setError('Please enter a valid 10-digit mobile number');
      return;
    }
    
    setError('');
    setStep('loading');
    
    try {
      const response = await fetch('/api/auth/send-otp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          // Consistently format phone number with +91 prefix
          phoneNumber: `+91${phoneNumber.trim()}` 
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to send OTP');
      }
      
      // Show OTP field
      setStep('otp');
      
      // Start countdown timer for resend
      startResendTimer();
      
      // For development, display the OTP in the console
      if (data.otp) {
        console.log('OTP for testing:', data.otp);
        // Optionally pre-fill OTP field for development
        setOtpCode(data.otp);
      }
      
      if (isResend) {
        showToast('OTP resent successfully');
      }
      
    } catch (err) {
      setError(err.message || 'Something went wrong. Please try again.');
      setStep('phone');
    }
  };
  
  const handleVerifyOTP = async () => {
    if (!otpCode || otpCode.length < 4) {
      setError('Please enter the verification code');
      return;
    }
    
    setError('');
    setStep('loading');
    
    try {
      const response = await fetch('/api/auth/verify-otp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          phoneNumber: `+91${phoneNumber.trim()}`,
          otpCode: otpCode.trim()
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to verify OTP');
      }
      
      // Store user data securely
      securelyStoreUserData(data);
      
      // Success! Notify parent component
      showToast('Login successful');
      
      if (onLoginSuccess) {
        onLoginSuccess(data.user);
      }
      
      // Redirect to the stored redirect URL or homepage after short delay
      setTimeout(() => {
        window.location.href = redirectUrl || '/';
      }, 1500);
      
    } catch (err) {
      setError(err.message || 'Invalid or expired verification code');
      setStep('otp');
    }
  };
  
  // Securely store user data
  const securelyStoreUserData = (authData) => {
    if (authData.user) {
      localStorage.setItem('user', JSON.stringify({
        id: authData.user.id,
        name: authData.user.name,
        phone: authData.user.phone
      }));
    }
    
    if (authData.token) {
      sessionStorage.setItem('authToken', authData.token);
    }
  };
  
  const startResendTimer = () => {
    setTimer(30);
    const interval = setInterval(() => {
      setTimer(prev => {
        if (prev <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };
  
  const handleResend = async () => {
    if (timer > 0 || isResending) return;
    
    setIsResending(true);
    await handleSendOTP(true);
    setIsResending(false);
  };
  
  const showToast = (message) => {
    // Create a toast notification
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-3 rounded-full text-sm font-medium shadow-lg z-50';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
      toast.style.opacity = '1';
    }, 10);
    
    // Remove after 3 seconds
    setTimeout(() => {
      toast.style.opacity = '0';
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 300);
    }, 3000);
  };

  return (
    <div className="w-full max-w-md mx-auto p-4">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">
          {step === 'phone' ? 'Login with Phone' : 'Verify Your Number'}
        </h2>
        <p className="text-gray-600">
          {step === 'phone' 
            ? 'Enter your phone number to continue'
            : 'We sent a verification code to your phone'}
        </p>
      </div>

      {error && (
        <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <span className="material-icons-round mr-2 text-red-500">error_outline</span>
          <span>{error}</span>
        </div>
      )}

      {step === 'phone' && (
        <div className="space-y-5">
          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <span className="material-icons-round text-gray-400">smartphone</span>
              </div>
              {/* Country code prefix */}
              <div className="absolute inset-y-0 left-9 flex items-center pointer-events-none">
                <span className="text-gray-500 font-medium border-r border-gray-300 pr-2">+91</span>
              </div>
              <input
                type="tel"
                id="phone"
                value={phoneNumber}
                onChange={handlePhoneChange}
                className="w-full pl-24 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#FF6B35] focus:border-[#FF6B35] outline-none transition-all"
                placeholder="Enter 10-digit number"
                inputMode="numeric"
                pattern="[0-9]*"
                maxLength="10"
                autoComplete="tel"
              />
              {/* Visual validation indicator */}
              {phoneNumber.length > 0 && (
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  {phoneNumber.length === 10 && validatePhoneNumber() ? (
                    <span className="material-icons-round text-green-500">check_circle</span>
                  ) : (
                    <span className="material-icons-round text-gray-300">info</span>
                  )}
                </div>
              )}
            </div>
            {/* Help text */}
            <p className="mt-1.5 text-xs text-gray-500 flex items-center">
              <span className="material-icons-round text-xs mr-1">info</span>
              Enter 10-digit mobile number without country code
            </p>
          </div>

          <button
            onClick={() => handleSendOTP()}
            disabled={step === 'loading' || !validatePhoneNumber()}
            className={`w-full py-3.5 text-white rounded-xl font-medium transition-colors flex items-center justify-center ${
              validatePhoneNumber() 
                ? 'bg-[#FF6B35] hover:bg-[#e55c28]'
                : 'bg-gray-300 cursor-not-allowed'
            }`}
          >
            {step === 'loading' ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
            ) : (
              <span className="material-icons-round mr-2">sms</span>
            )}
            Send Verification Code
          </button>
        </div>
      )}

      {step === 'otp' && (
        <div className="space-y-5">
          <div className="flex items-center mb-4 p-3 bg-blue-50 rounded-lg border border-blue-100">
            <span className="material-icons-round text-blue-600 mr-2">smartphone</span>
            <span className="text-sm text-gray-700">
              Verification code sent to: <b>+91 {phoneNumber}</b>
            </span>
            <button 
              onClick={() => setStep('phone')} 
              className="ml-auto text-sm text-[#FF6B35] hover:text-[#e55c28] transition-colors"
            >
              Change
            </button>
          </div>

          <div>
            <label htmlFor="otp" className="block text-sm font-medium text-gray-700 mb-2">
              Verification Code
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <span className="material-icons-round text-gray-400">lock</span>
              </div>
              <input
                type="text"
                id="otp"
                value={otpCode}
                onChange={(e) => setOtpCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                maxLength={6}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#FF6B35] focus:border-[#FF6B35] outline-none transition-all text-xl tracking-wide text-center font-medium"
                placeholder="Enter 6-digit code"
                inputMode="numeric"
                autoComplete="one-time-code"
                autoFocus
              />
            </div>
          </div>

          <button
            onClick={handleVerifyOTP}
            disabled={step === 'loading' || otpCode.length !== 6}
            className={`w-full py-3.5 text-white rounded-xl font-medium transition-colors flex items-center justify-center ${
              otpCode.length === 6 
                ? 'bg-[#FF6B35] hover:bg-[#e55c28]'
                : 'bg-gray-300 cursor-not-allowed'
            }`}
          >
            {step === 'loading' ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
            ) : (
              <span className="material-icons-round mr-2">check_circle</span>
            )}
            Verify Code
          </button>

          <div className="text-center mt-4">
            <p className="text-sm text-gray-500 mb-1">
              Didn't receive the code?
            </p>
            <button
              onClick={handleResend}
              disabled={timer > 0 || isResending}
              className={`text-sm font-medium ${
                timer > 0 || isResending
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-[#FF6B35] hover:text-[#e55c28]'
              }`}
            >
              {timer > 0
                ? `Resend code in ${timer}s`
                : isResending
                ? 'Resending...'
                : 'Resend Code'}
            </button>
          </div>
        </div>
      )}

      {step === 'loading' && (
        <div className="flex flex-col items-center justify-center py-8">
          <div className="w-12 h-12 border-4 border-gray-200 border-t-[#FF6B35] rounded-full animate-spin mb-4"></div>
          <p className="text-gray-600 font-medium">Processing...</p>
        </div>
      )}
    </div>
  );
}
