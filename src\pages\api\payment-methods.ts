import type { APIRoute } from 'astro';
import { getPaymentMethodSettings } from '../../db/database';

export const prerender = false;

/**
 * Get payment method settings
 * This endpoint is public and can be accessed without authentication
 */
export const GET: APIRoute = async ({ locals }) => {
  try {
    const settings = await getPaymentMethodSettings(locals.runtime.env);
    
    return new Response(JSON.stringify({
      success: true,
      settings
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error getting payment method settings:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to get payment method settings',
      settings: {
        online_payment_enabled: true,
        cash_on_delivery_enabled: true
      }
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
