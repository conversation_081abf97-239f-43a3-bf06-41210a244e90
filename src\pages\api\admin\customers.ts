import type { APIRoute } from 'astro';
import { adminAuthMiddleware } from '../../../middleware/auth';

export const prerender = false;

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';
    const sortField = url.searchParams.get('sortField') || 'name';
    const sortDirection = url.searchParams.get('sortDirection') || 'asc';

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Build the query
    let query = `
      SELECT
        u.id,
        u.name,
        u.phone_number,
        u.email,
        u.created_at as joined_date,
        (SELECT COUNT(*) FROM orders WHERE user_id = u.id) as orders_count,
        (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE user_id = u.id) as total_spent,
        (SELECT MAX(created_at) FROM orders WHERE user_id = u.id) as last_active
      FROM users u
      WHERE 1=1
    `;

    const queryParams: any[] = [];

    // Add search condition if provided
    if (search) {
      query += ` AND (
        u.name LIKE ? OR
        u.phone_number LIKE ? OR
        u.email LIKE ?
      )`;
      const searchParam = `%${search}%`;
      queryParams.push(searchParam, searchParam, searchParam);
    }

    // Count total customers for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM users u
      WHERE 1=1
      ${search ? ' AND (u.name LIKE ? OR u.phone_number LIKE ? OR u.email LIKE ?)' : ''}
    `;

    const countResult = await locals.runtime.env.SNACKSWIFT_DB.prepare(countQuery)
      .bind(...(search ? [
        `%${search}%`,
        `%${search}%`,
        `%${search}%`
      ] : []))
      .all();

    const totalCustomers = Number(countResult.results?.[0]?.total) || 0;

    // Add sorting
    query += ` ORDER BY ${sortField} ${sortDirection === 'asc' ? 'ASC' : 'DESC'}`;

    // Add pagination
    query += ` LIMIT ? OFFSET ?`;
    queryParams.push(limit, offset);

    // Execute main query
    const result = await locals.runtime.env.SNACKSWIFT_DB.prepare(query)
      .bind(...queryParams)
      .all();

    return new Response(
      JSON.stringify({
        success: true,
        customers: result.results || [],
        pagination: {
          page,
          limit,
          total: totalCustomers,
          totalPages: Math.ceil(totalCustomers / limit)
        }
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

  } catch (error) {
    console.error('Error fetching customers:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Failed to fetch customers',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
};
