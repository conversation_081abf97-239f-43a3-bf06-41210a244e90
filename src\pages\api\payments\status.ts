import type { APIRoute } from 'astro';
import { getPaymentTransactionByPaymentId } from '../../../db/database';
import { authMiddleware } from '../../../middleware/auth';

export const prerender = false;

/**
 * Get status of a payment transaction
 * This endpoint is used to check payment status after user returns from payment gateway
 */
export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Authenticate user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }
    
    // Get transaction ID from query parameters
    const url = new URL(request.url);
    const transactionId = url.searchParams.get('transactionId');
    
    if (!transactionId) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Transaction ID is required' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Get transaction from database
    const transaction = await getPaymentTransactionByPaymentId(
      locals.runtime.env, 
      transactionId
    );
    
    if (!transaction) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Transaction not found' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Return transaction status
    return new Response(JSON.stringify({ 
      success: true,
      transaction: {
        id: transaction.id,
        order_id: transaction.order_id,
        payment_id: transaction.payment_id,
        amount: transaction.amount,
        status: transaction.status,
        created_at: transaction.created_at,
        updated_at: transaction.updated_at
      }
    }), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate' // Don't cache payment status
      }
    });
    
  } catch (error) {
    console.error('Error checking payment status:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Failed to check payment status' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};