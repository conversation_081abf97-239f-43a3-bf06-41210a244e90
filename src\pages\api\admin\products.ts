import type { APIRoute } from "astro";
import { getFilteredProducts } from "../../../db/database";
export const prerender = false;
export const GET: APIRoute = async ({ request, locals }) => {
  // Check admin authentication
  // In a production app, uncomment this code
  /*
  if (!locals.user || !locals.user.isAdmin) {
    return new Response(JSON.stringify({
      error: 'Unauthorized'
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
  */

  const url = new URL(request.url);
  const params = url.searchParams;

  try {
    // Get query parameters with defaults
    const page = parseInt(params.get("page") || "1");
    const limit = parseInt(params.get("limit") || "12"); // Higher limit for admin view
    const category = params.get("category") || undefined;
    const search = params.get("search") || "";
    const sort = params.get("sort") || "id";
    const minPrice = parseFloat(params.get("minPrice") || "0");
    const maxPrice = parseFloat(params.get("maxPrice") || "10000");

    // Get products with filters
    const result = await getFilteredProducts(locals.runtime.env, {
      page,
      limit,
      category,
      search,
      sort,
      minPrice,
      maxPrice,
      admin: true,
    });

    return new Response(
      JSON.stringify({
        products: result.products,
        totalCount: result.totalCount,
        currentPage: result.currentPage,
        totalPages: result.totalPages,
        hasMore: result.hasMore,
      }),
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  } catch (error) {
    console.error("Error in admin products endpoint:", error);
    return new Response(
      JSON.stringify({
        error: "Failed to fetch products",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }
};

// Create new product
export const POST: APIRoute = async ({ request, locals }) => {
  // Check admin authentication
  // In a production app, uncomment this code
  /*
  if (!locals.user || !locals.user.isAdmin) {
    return new Response(JSON.stringify({
      error: 'Unauthorized'
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
  */

  try {
    const product = await request.json();
    const env = locals.runtime.env;

    // Validate required fields
    const requiredFields = [
      "name",
      "price",
      "description",
      "category_id",
      "unit_type",
      "unit_value",
    ];
    for (const field of requiredFields) {
      if (!product[field]) {
        return new Response(
          JSON.stringify({
            error: `Missing required field: ${field}`,
          }),
          {
            status: 400,
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
      }
    }

    // Generate URL slug if not provided
    if (!product.url_slug) {
      product.url_slug =
        product.name
          .toLowerCase()
          .replace(/\s+/g, "-")
          .replace(/[^a-z0-9-]/g, "") +
        Math.random().toString(36).substring(2, 7);
    }

    // Insert the product into the database
    const result = await env.SNACKSWIFT_DB.prepare(
      `
      INSERT INTO products (
        name, url_slug, description, price, old_price,
        image, category_id, is_featured, is_new, is_on_sale,
        is_available, unit_type, unit_value, stock_quantity, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    )
      .bind(
        product.name,
        product.url_slug,
        product.description,
        product.price,
        product.old_price || null,
        product.image || null,
        product.category_id,
        product.is_featured ? 1 : 0,
        product.is_new ? 1 : 0,
        product.is_on_sale ? 1 : 0,
        product.is_available === false ? 0 : 1, // Default to available if not specified
        product.unit_type,
        product.unit_value,
        product.stock || 0, // Add stock quantity
        new Date().toISOString()
      )
      .run();

    if (!result || !result.success) {
      throw new Error("Failed to insert product");
    }

    // Get the newly inserted product ID
    const productId = result.meta.last_row_id;

    // Retrieve the newly created product
    const { results } = await env.SNACKSWIFT_DB.prepare(
      `
      SELECT p.*, c.name as category
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.id = ?
    `
    )
      .bind(productId)
      .all();

    if (!results || results.length === 0) {
      throw new Error("Product created but unable to retrieve");
    }

    return new Response(
      JSON.stringify({
        success: true,
        product: results[0],
        message: "Product created successfully",
      }),
      {
        status: 201,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  } catch (error) {
    console.error("Error creating product:", error);
    return new Response(
      JSON.stringify({
        error: "Failed to create product",
        details: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }
};
