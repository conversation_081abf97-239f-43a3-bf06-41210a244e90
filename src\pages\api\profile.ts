import { getUserById, updateUser } from '../../db/database';
import { authMiddleware } from '../../middleware/auth';

export const prerender = false;

/**
 * API endpoint to handle user profile
 */
export async function GET({ locals, request }) {
  // Check authentication first
  const authResult = await authMiddleware({ request });
  if (authResult instanceof Response) {
    return authResult; // Return 401 if not authenticated
  }
  
  const user = authResult.user;
  
  if (!locals.runtime || !locals.runtime.env) {
    return new Response(JSON.stringify({ error: "Runtime environment not available" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    // Get full user details
    const userDetails = await getUserById(locals.runtime.env, user.id);
    
    if (!userDetails) {
      return new Response(JSON.stringify({ error: "User not found" }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Remove sensitive data
    delete userDetails.password_hash;
    
    return new Response(JSON.stringify({ user: userDetails }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error("Error in profile GET API:", error);
    return new Response(JSON.stringify({ error: "Failed to get user profile" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function PUT({ locals, request }) {
  // Check authentication first
  const authResult = await authMiddleware({ request });
  if (authResult instanceof Response) {
    return authResult; // Return 401 if not authenticated
  }
  
  const user = authResult.user;
  
  if (!locals.runtime || !locals.runtime.env) {
    return new Response(JSON.stringify({ error: "Runtime environment not available" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  try {
    // Get profile data from request
    const data = await request.json();
    
    // Basic validation
    if (!data.name || !data.name.trim()) {
      return new Response(JSON.stringify({ error: "Name is required" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    if (!data.email || !data.email.trim()) {
      return new Response(JSON.stringify({ error: "Email is required" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Email validation
    if (!/\S+@\S+\.\S+/.test(data.email)) {
      return new Response(JSON.stringify({ error: "Email is invalid" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Prepare user data for update
    const userData = {
      id: user.id,
      name: data.name.trim(),
      email: data.email.trim()
    };
    
    // Update the user in database
    const updatedUser = await updateUser(locals.runtime.env, userData);
    
    if (!updatedUser) {
      return new Response(JSON.stringify({ error: "Failed to update user profile" }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Remove sensitive data
    const safeUserData = {
      id: updatedUser.id,
      name: updatedUser.name,
      email: updatedUser.email
    };
    
    return new Response(JSON.stringify({ 
      success: true,
      user: safeUserData 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error("Error in profile PUT API:", error);
    return new Response(JSON.stringify({ error: "Failed to update user profile" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
