import { useState, useEffect } from "react";
import DataTable from "./DataTable";

export default function CustomersManager() {
  const [customers, setCustomers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showCustomerDetails, setShowCustomerDetails] = useState(false);
  const [sortField, setSortField] = useState("name");
  const [sortDirection, setSortDirection] = useState("asc");
  const [error, setError] = useState(null);

  const ITEMS_PER_PAGE = 10;

  // Fetch customers on component mount and when dependencies change
  useEffect(() => {
    fetchCustomers();
  }, [currentPage, searchTerm, sortField, sortDirection]);

  // Fetch customers from the API
  const fetchCustomers = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Call our API client to get customers with pagination, search and sorting
      const result = await window.ApiClient.getAdminCustomers(
        currentPage,
        ITEMS_PER_PAGE,
        searchTerm,
        sortField,
        sortDirection
      );

      // Set customers from the response
      setCustomers(result.customers || []);

      // Set pagination data
      if (result.pagination) {
        setTotalPages(result.pagination.totalPages || 1);
        setTotalItems(result.pagination.total || 0);
      }

      setIsLoading(false);
    } catch (error) {
      console.error("Failed to fetch customers:", error);
      setError("Failed to load customers. Please try again.");
      setIsLoading(false);
    }
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleSort = (field) => {
    if (field === sortField) {
      // Toggle direction if clicking on the same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Default to ascending for a new sort field
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const handleSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset to first page on search
  };

  const handleViewDetails = (customer) => {
    setSelectedCustomer(customer);
    setShowCustomerDetails(true);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
    }).format(date);
  };

  const formatCurrency = (value) => {
    if (value === null || value === undefined) return "₹0.00";
    return `₹${parseFloat(value).toFixed(2)}`;
  };

  const formatTimeAgo = (dateString) => {
    if (!dateString) return "Never";

    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now - date;
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays < 0) {
      // Handle future dates (for mock data)
      return "Online now";
    }

    if (diffInDays === 0) {
      return "Today";
    }

    if (diffInDays === 1) {
      return "Yesterday";
    }

    if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    }

    if (diffInDays < 30) {
      return `${Math.floor(diffInDays / 7)} weeks ago`;
    }

    return `${Math.floor(diffInDays / 30)} months ago`;
  };

  // Define columns for the DataTable
  const columns = [
    {
      key: "name",
      label: "Customer",
      sortable: true,
      render: (customer) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-orange-100 flex items-center justify-center text-orange-600 font-semibold">
              {customer.name ? customer.name.charAt(0) : "?"}
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">
              {customer.name}
            </div>
            <div className="text-sm text-gray-500">
              Joined {formatDate(customer.joined_date)}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "contact",
      label: "Contact",
      render: (customer) => (
        <div>
          <div className="text-sm text-gray-900">
            {customer.email || "No email"}
          </div>
          <div className="text-sm text-gray-500">{customer.phone_number}</div>
        </div>
      ),
    },
    {
      key: "orders_count",
      label: "Orders",
      sortable: true,
      render: (customer) => (
        <div className="text-sm font-medium text-gray-900">
          {customer.orders_count || 0}
        </div>
      ),
    },
    {
      key: "total_spent",
      label: "Total Spent",
      sortable: true,
      render: (customer) => (
        <div className="text-sm text-gray-900">
          {formatCurrency(customer.total_spent)}
        </div>
      ),
    },
    {
      key: "last_active",
      label: "Last Active",
      sortable: true,
      render: (customer) => (
        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          {formatTimeAgo(customer.last_active)}
        </span>
      ),
    },

  ];

  return (
    <div className="space-y-6">
      {/* Header with actions */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
        <h1 className="text-2xl font-semibold text-gray-800">Customers</h1>

        <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
          <button className="flex items-center justify-center px-4 py-2 bg-orange-500 text-white rounded-lg shadow-sm hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2">
            <span className="material-icons-round mr-2 text-sm">
              file_download
            </span>
            Export
          </button>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="material-icons-round text-red-400">
                error_outline
              </span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Search Bar */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col md:flex-row md:justify-between md:items-center space-y-4 md:space-y-0">
          <div className="relative rounded-md shadow-sm w-full md:max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="material-icons-round text-gray-400 text-lg">
                search
              </span>
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              placeholder="Search customers by name, email, or phone..."
              className="focus:ring-orange-500 focus:border-orange-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md py-2.5"
            />
          </div>
        </div>
      </div>

      {/* Customers Table */}
      <DataTable
        columns={columns}
        data={customers}
        isLoading={isLoading}
        pagination={true}
        searchable={false} // We're handling search separately
        sortable={true}
        onSort={handleSort}
        paginationConfig={{
          currentPage: currentPage,
          totalItems: totalItems,
          itemsPerPage: ITEMS_PER_PAGE,
          totalPages: totalPages,
          onPageChange: handlePageChange,
        }}
        emptyMessage="No customers found"
        actions={(customer) => (
          <div>
            <button
              onClick={() => handleViewDetails(customer)}
              className="text-orange-600 hover:text-orange-900 mr-4 cursor-pointer"
            >
              View
            </button>
          </div>
        )}
      />

      {/* Customer Details Modal */}
      {showCustomerDetails && selectedCustomer && (
        <>
          {/* Modal backdrop */}
          <div
            className="fixed inset-0 bg-gray-500 bg-opacity-75 z-40"
            onClick={() => setShowCustomerDetails(false)}
          ></div>

          {/* Modal container */}
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-center justify-center min-h-screen p-4">
              <div className="bg-white rounded-xl shadow-xl w-full max-w-2xl mx-auto relative">
                <div className="bg-gray-50 px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-gray-900">
                    Customer Details
                  </h3>
                  <button
                    onClick={() => setShowCustomerDetails(false)}
                    className="text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500 rounded-full p-1"
                  >
                    <span className="material-icons-round">close</span>
                  </button>
                </div>

                <div className="px-6 py-5">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <div className="h-16 w-16 rounded-full bg-orange-100 flex items-center justify-center text-orange-600 text-xl font-semibold">
                        {selectedCustomer.name
                          ? selectedCustomer.name.charAt(0)
                          : "?"}
                      </div>
                    </div>

                    <div className="ml-6">
                      <h4 className="text-xl font-semibold text-gray-900">
                        {selectedCustomer.name}
                      </h4>
                      <div className="mt-1 flex items-center text-sm text-gray-500">
                        <span className="material-icons-round text-gray-400 mr-1 text-xs">
                          email
                        </span>
                        {selectedCustomer.email || "No email"}
                      </div>
                      <div className="mt-1 flex items-center text-sm text-gray-500">
                        <span className="material-icons-round text-gray-400 mr-1 text-xs">
                          phone
                        </span>
                        {selectedCustomer.phone_number}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                    <div className="bg-orange-50 rounded-lg p-4">
                      <div className="text-sm font-medium text-gray-500">
                        Total Orders
                      </div>
                      <div className="mt-1 text-2xl font-semibold text-gray-900">
                        {selectedCustomer.orders_count || 0}
                      </div>
                    </div>

                    <div className="bg-green-50 rounded-lg p-4">
                      <div className="text-sm font-medium text-gray-500">
                        Total Spent
                      </div>
                      <div className="mt-1 text-2xl font-semibold text-gray-900">
                        {formatCurrency(selectedCustomer.total_spent)}
                      </div>
                    </div>

                    <div className="bg-blue-50 rounded-lg p-4">
                      <div className="text-sm font-medium text-gray-500">
                        Customer Since
                      </div>
                      <div className="mt-1 text-2xl font-semibold text-gray-900">
                        {formatDate(selectedCustomer.joined_date)}
                      </div>
                    </div>
                  </div>

                  <div className="bg-yellow-50 rounded-lg p-4 mt-6">
                    <div className="text-sm font-medium text-gray-500">
                      Last Active
                    </div>
                    <div className="mt-1 text-2xl font-semibold text-gray-900">
                      {formatTimeAgo(selectedCustomer.last_active)}
                    </div>
                  </div>

                  {/* Recent Orders - Would be populated with real data in a full implementation */}
                  {/* <div className="mt-8">
                    <h4 className="text-lg font-medium text-gray-800 mb-3">Recent Orders</h4>
                    <div className="border rounded-lg overflow-hidden">
                      <div className="text-center py-8 text-gray-500">
                        <div className="flex justify-center mb-4">
                          <span className="material-icons-round text-orange-400 text-3xl">receipt_long</span>
                        </div>
                        <p>Recent orders will appear here</p>
                      </div>
                    </div>
                  </div> */}

                  {/* Call to Action Buttons */}
                  <div className="mt-8 flex justify-end space-x-3">
                    <button
                      onClick={() => setShowCustomerDetails(false)}
                      className="px-4 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50"
                    >
                      Close
                    </button>
                    {/* <button
                      className="px-4 py-2 bg-orange-600 border border-transparent rounded-lg font-medium text-white hover:bg-orange-700"
                    >
                      Send Message
                    </button> */}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
