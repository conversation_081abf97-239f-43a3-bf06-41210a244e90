export default function StatsCard({
  title,
  value,
  change,
  changeType,
  icon,
  color = "blue",
}) {
  // Define color variants based on the color prop
  const colorVariants = {
    blue: {
      bgLight: "bg-blue-50",
      bgDark: "bg-blue-500",
      text: "text-blue-700",
      iconBg: "text-blue-500",
    },
    green: {
      bgLight: "bg-green-50",
      bgDark: "bg-green-500",
      text: "text-green-700",
      iconBg: "text-green-500",
    },
    orange: {
      bgLight: "bg-orange-50",
      bgDark: "bg-orange-500",
      text: "text-orange-700",
      iconBg: "text-orange-500",
    },
    red: {
      bgLight: "bg-red-50",
      bgDark: "bg-red-500",
      text: "text-red-700",
      iconBg: "text-red-500",
    },
    purple: {
      bgLight: "bg-purple-50",
      bgDark: "bg-purple-500",
      text: "text-purple-700",
      iconBg: "text-purple-500",
    },
  };

  // Get the current color variant
  const currentColor = colorVariants[color] || colorVariants.blue;

  // Determine change indicator style
  const getChangeStyle = () => {
    if (changeType === "increase") {
      return {
        icon: "trending_up",
        iconClass: "text-green-500",
        bgClass: "bg-green-50",
      };
    } else {
      return {
        icon: "trending_down",
        iconClass: "text-red-500",
        bgClass: "bg-red-50",
      };
    }
  };

  const changeStyle = getChangeStyle();

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="p-5">
        <div className="flex items-center">
          <div
            className={`w-12 h-12 rounded-lg ${currentColor.bgLight} flex items-center justify-center`}
          >
            <span
              className={`material-icons-round text-2xl ${currentColor.iconBg}`}
            >
              {icon}
            </span>
          </div>
          <div className="ml-5">
            <h3 className="text-sm font-medium text-gray-500">{title}</h3>
            <div className="flex items-end">
              <p className="text-2xl font-semibold text-gray-800">{value}</p>
              {change && (
                <div
                  className={`flex items-center ml-2 mb-0.5 px-2 py-0.5 rounded-full ${changeStyle.bgClass}`}
                >
                  <span
                    className={`material-icons-round text-xs ${changeStyle.iconClass} mr-1`}
                  >
                    {changeStyle.icon}
                  </span>
                  <span
                    className={`text-xs font-medium ${changeStyle.iconClass}`}
                  >
                    {change}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
