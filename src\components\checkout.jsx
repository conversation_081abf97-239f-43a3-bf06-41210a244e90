import React, { useState, useEffect } from "react";
import LoadingOverlay from "./common/LoadingOverlay";

// Format currency values
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2
  }).format(amount);
};

export default function Checkout() {
  const [cartItems, setCartItems] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingAddresses, setLoadingAddresses] = useState(false);
  const [loadingLocations, setLoadingLocations] = useState(false);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentMethodSettings, setPaymentMethodSettings] = useState({
    online_payment_enabled: true,
    cash_on_delivery_enabled: true
  });
  const [pricing, setPricing] = useState({
    subtotal: 0,
    deliveryFee: 2.99,
    discount: 0,
    total: 0,
  });
  const [step, setStep] = useState(1);
  const [savedAddresses, setSavedAddresses] = useState([]);
  const [selectedAddressId, setSelectedAddressId] = useState(null);
  const [showNewAddressForm, setShowNewAddressForm] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState("online");
  const [couponInfo, setCouponInfo] = useState(null);
  const [locations, setLocations] = useState([]);
  const [selectedLocationId, setSelectedLocationId] = useState(null);

  // Form states
  const [shippingInfo, setShippingInfo] = useState({
    fullName: "",
    phone: "",
    address: "",
    city: "",
    zipCode: "",
    instructions: "",
    saveAddress: true,
    setAsDefault: false,
  });

  const [shippingErrors, setShippingErrors] = useState({});

  // Fetch available order locations
  const fetchLocations = async () => {
    setLoadingLocations(true);
    try {
      if (typeof window !== "undefined") {
        // Check if ApiClient exists and has the getOrderLocations method
        if (window.ApiClient && typeof window.ApiClient.getOrderLocations === 'function') {
          console.log('Fetching delivery locations...');
          const response = await window.ApiClient.getOrderLocations();

          if (response.success && response.locations) {
            console.log('Loaded delivery locations:', response.locations);
            setLocations(response.locations);

            // Check if CartUtils and getDeliveryLocation function exist
            try {
              if (window.CartUtils && typeof window.CartUtils.getDeliveryLocation === 'function') {
                const savedLocation = window.CartUtils.getDeliveryLocation();
                console.log('Saved location from CartUtils:', savedLocation);

                if (savedLocation && savedLocation.id) {
                  // Use the saved location if it exists
                  console.log('Using saved location:', savedLocation.name);
                  setSelectedLocationId(savedLocation.id);

                  // Find the location in the response to ensure it exists
                  const locationExists = response.locations.some(loc => loc.id === savedLocation.id);
                  if (locationExists) {
                    // Update pricing with the selected location after a short delay
                    // to ensure the state is updated
                    setTimeout(() => {
                      console.log('Updating pricing with saved location after delay');
                      updatePricingWithLocation(savedLocation.id);
                    }, 300);
                  } else {
                    console.log('Saved location no longer exists, selecting first available');
                    if (response.locations.length > 0) {
                      setSelectedLocationId(response.locations[0].id);
                      setTimeout(() => {
                        updatePricingWithLocation(response.locations[0].id);
                      }, 300);
                    }
                  }
                } else if (response.locations.length > 0) {
                  // Otherwise select the first location by default if available
                  console.log('No saved location, selecting first available:', response.locations[0].name);
                  setSelectedLocationId(response.locations[0].id);

                  // Update pricing with the selected location after a short delay
                  setTimeout(() => {
                    console.log('Updating pricing with first location after delay');
                    updatePricingWithLocation(response.locations[0].id);
                  }, 300);
                }
              } else {
                // If getDeliveryLocation is not available, just use the first location
                if (response.locations.length > 0) {
                  console.log('CartUtils.getDeliveryLocation not available, using first location');
                  setSelectedLocationId(response.locations[0].id);

                  // Try to update pricing anyway
                  setTimeout(() => {
                    if (typeof window.CartUtils?.setDeliveryLocation === 'function') {
                      updatePricingWithLocation(response.locations[0].id);
                    }
                  }, 500);
                }
                console.warn('CartUtils.getDeliveryLocation not available yet');
              }
            } catch (cartError) {
              console.warn('Error accessing CartUtils:', cartError);
              // Still set a default location
              if (response.locations.length > 0) {
                setSelectedLocationId(response.locations[0].id);
              }
            }
          } else {
            console.warn('No locations returned from API or request failed');
          }
        } else {
          console.warn('ApiClient.getOrderLocations not available yet');
          // Wait a bit and try again if the API client isn't ready
          setTimeout(fetchLocations, 1000);
        }
      }
    } catch (error) {
      console.error("Error fetching locations:", error);
      showNotification("Could not load available locations", "error");
    } finally {
      setLoadingLocations(false);
    }
  };

  // Update pricing when location changes
  const updatePricingWithLocation = async (locationId) => {
    if (typeof window !== "undefined" && window.CartUtils) {
      try {
        // Find the location name
        const location = locations.find(loc => loc.id === locationId);
        if (location) {
          console.log('Updating pricing with location:', location.name, '(ID:', locationId, ')');

          // Check if setDeliveryLocation function exists
          if (typeof window.CartUtils.setDeliveryLocation === 'function') {
            // Save the location in CartUtils
            window.CartUtils.setDeliveryLocation(locationId, location.name);

            // Force a pricing update by calling updatePricing directly
            if (typeof window.CartUtils.updatePricing === 'function') {
              // Wait for the API call to complete
              const updatedPricing = await window.CartUtils.updatePricing();
              console.log('Updated pricing after location change:', updatedPricing);

              // Ensure we're using the latest pricing data
              setPricing(updatedPricing);

              // Add a small delay and check pricing again to ensure it's updated
              setTimeout(async () => {
                const finalPricing = await window.CartUtils.updatePricing();
                console.log('Final pricing check:', finalPricing);
                setPricing(finalPricing);
              }, 500);
            } else {
              // Fallback to getPricing if updatePricing is not available
              const updatedPricing = window.CartUtils.getPricing();
              setPricing(updatedPricing);
            }

            // Show notification with delivery fee information
            if (location.delivery_fee !== undefined) {
              const feeDisplay = parseFloat(location.delivery_fee) > 0
                ? `₹${parseFloat(location.delivery_fee).toFixed(2)} delivery fee`
                : 'Free delivery';
              showNotification(`Delivery area set to ${location.name} (${feeDisplay})`);
            } else {
              showNotification(`Delivery area set to ${location.name}`);
            }
          } else {
            console.warn('CartUtils.setDeliveryLocation not available yet');
            // Still update the UI state
            setSelectedLocationId(locationId);
          }
        }
      } catch (error) {
        console.error('Error updating pricing with location:', error);
        showNotification('Could not update delivery fee', 'error');
      }
    }
  };

  // Load cart items, pricing, addresses, locations, and payment method settings on component mount
  useEffect(() => {
    // Load cart items and pricing info
    const timer = setTimeout(async () => {
      if (typeof window !== "undefined" && window.CartUtils) {
        const items = window.CartUtils.getCartItems();
        const currentPricing = window.CartUtils.getPricing();
        const coupon = await window.CartUtils.getAppliedCoupon();

        setCartItems(items);
        setPricing(currentPricing);
        setCouponInfo(coupon);

        // Redirect to cart if empty
        if (items.length === 0) {
          window.location.href = "/cart";
          return;
        }
      }
      setIsLoading(false);

      // Load user addresses, available locations, and payment method settings
      fetchUserAddresses();
      fetchLocations();
      fetchPaymentMethodSettings();
    }, 300);

    // Add event listener for pricing updates
    const handlePricingUpdate = (event) => {
      console.log('Checkout: Pricing updated event received:', event.detail);
      setPricing(event.detail);
    };

    if (typeof window !== "undefined") {
      window.addEventListener('pricing-updated', handlePricingUpdate);
    }

    return () => {
      clearTimeout(timer);
      if (typeof window !== "undefined") {
        window.removeEventListener('pricing-updated', handlePricingUpdate);
      }
    };
  }, []);

  // Fetch payment method settings
  const fetchPaymentMethodSettings = async () => {
    try {
      const response = await window.ApiClient.getPaymentMethodSettings();
      if (response.success && response.settings) {
        setPaymentMethodSettings(response.settings);

        // If the current payment method is disabled, switch to an enabled one
        if (paymentMethod === "online" && !response.settings.online_payment_enabled) {
          if (response.settings.cash_on_delivery_enabled) {
            setPaymentMethod("cash");
          }
        } else if (paymentMethod === "cash" && !response.settings.cash_on_delivery_enabled) {
          if (response.settings.online_payment_enabled) {
            setPaymentMethod("online");
          }
        }
      }
    } catch (error) {
      console.error("Error fetching payment method settings:", error);
    }
  };

  // Fetch user addresses from the API
  const fetchUserAddresses = async () => {
    setLoadingAddresses(true);
    try {
      // Check if the user is authenticated first
      if (typeof window !== "undefined" && window.ApiClient) {
        if (!window.ApiClient.isAuthenticated()) {
          // Redirect to login with return URL
          const currentPath = window.location.pathname;
          window.location.href = `/login?redirect=${encodeURIComponent(
            currentPath
          )}`;
          return;
        }

        // User is authenticated, fetch addresses
        const data = await window.ApiClient.getAddresses();

        if (data.addresses && Array.isArray(data.addresses)) {
          setSavedAddresses(data.addresses);

          // Select default address if available
          const defaultAddress = data.addresses.find((addr) => addr.is_default);
          if (defaultAddress) {
            setSelectedAddressId(defaultAddress.id);

            // Pre-populate shipping form with default address for easy editing
            setShippingInfo({
              fullName: defaultAddress.full_name,
              phone: defaultAddress.phone,
              address: defaultAddress.address,
              city: defaultAddress.city,
              zipCode: defaultAddress.zip_code,
              instructions: defaultAddress.instructions || "",
              saveAddress: false,
              setAsDefault: false,
            });
          } else if (data.addresses.length > 0) {
            // If no default, select the first address
            // setSelectedAddressId(data.addresses[0].id);
          }
        }
      } else {
        throw new Error("API client not available");
      }
    } catch (error) {
      console.error("Error fetching addresses:", error);
      // Check if it's an authentication error
      if (error.message === "Authentication required") {
        // Save current URL and redirect to login
        const currentPath = window.location.pathname;
        showNotification("Please log in to continue", "info");
        setTimeout(() => {
          window.location.href = `/login?redirect=${encodeURIComponent(
            currentPath
          )}`;
        }, 1500);
        return;
      }

      // Show a friendly error message for other errors
      showNotification("Could not load saved addresses", "error");
    } finally {
      setLoadingAddresses(false);
    }
  };

  // Save a new address
  const saveNewAddress = async () => {
    if (!validateShippingForm()) {
      return false;
    }

    // Check if user is authenticated
    if (
      typeof window !== "undefined" &&
      window.ApiClient &&
      !window.ApiClient.isAuthenticated()
    ) {
      // Save form data in sessionStorage to restore after login
      try {
        sessionStorage.setItem(
          "pendingAddressData",
          JSON.stringify(shippingInfo)
        );
      } catch (e) {
        console.error("Failed to save address data:", e);
      }

      // Redirect to login with return URL
      const currentPath = window.location.pathname;
      showNotification("Please log in to save addresses", "info");
      setTimeout(() => {
        window.location.href = `/login?redirect=${encodeURIComponent(
          currentPath
        )}`;
      }, 1500);
      return false;
    }

    setLoadingAddresses(true);
    try {
      const addressData = {
        full_name: shippingInfo.fullName,
        phone: shippingInfo.phone,
        address: shippingInfo.address,
        city: shippingInfo.city,
        zip_code: shippingInfo.zipCode,
        instructions: shippingInfo.instructions,
        is_default: shippingInfo.setAsDefault,
      };

      // Use the secure API client instead of fetch directly
      if (typeof window !== "undefined" && window.ApiClient) {
        const data = await window.ApiClient.addAddress(addressData);

        if (data.address) {
          // Update the addresses list
          await fetchUserAddresses();
          // Select the new address
          setSelectedAddressId(data.address.id);
          // Hide the form
          setShowNewAddressForm(false);

          showNotification("Address saved successfully");
          return true;
        }
      } else {
        throw new Error("API client not available");
      }

      return false;
    } catch (error) {
      console.error("Error saving address:", error);
      // Check if it's an authentication error
      if (error.message === "Authentication required") {
        showNotification("Please log in to save addresses", "error");
        setTimeout(() => {
          window.location.href = "/login?redirect=/checkout";
        }, 2000);
        return false;
      }

      showNotification("Could not save address", "error");
      return false;
    } finally {
      setLoadingAddresses(false);
    }
  };

  // Update state when selecting a saved address
  const selectAddress = (addressId) => {
    setSelectedAddressId(addressId);

    // Update shipping info form with the selected address
    const selectedAddress = savedAddresses.find(
      (addr) => addr.id === addressId
    );
    if (selectedAddress) {
      setShippingInfo({
        fullName: selectedAddress.full_name,
        phone: selectedAddress.phone,
        address: selectedAddress.address,
        city: selectedAddress.city,
        zipCode: selectedAddress.zip_code,
        instructions: selectedAddress.instructions || "",
        saveAddress: false,
        setAsDefault: false,
      });
    }
  };

  // Handle shipping form changes
  const handleShippingChange = (e) => {
    const { name, value, type, checked } = e.target;
    setShippingInfo({
      ...shippingInfo,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  // Validate shipping form
  const validateShippingForm = () => {
    const errors = {};
    if (!shippingInfo.fullName.trim()) errors.fullName = "Name is required";
    if (!shippingInfo.phone.trim()) errors.phone = "Phone is required";
    if (!shippingInfo.address.trim()) errors.address = "Address is required";
    if (!shippingInfo.city.trim()) errors.city = "City is required";
    if (!shippingInfo.zipCode.trim()) errors.zipCode = "ZIP code is required";

    setShippingErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission for each step
  const handleContinue = async () => {
    if (step === 1) {
      let isValid = false;

      // Validate location selection - now a required step
      if (locations.length > 0 && !selectedLocationId) {
        showNotification("Please select a delivery area", "error");

        // Scroll to the location section
        const locationSection = document.querySelector('[data-section="delivery-locations"]');
        if (locationSection) {
          locationSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        return;
      }

      if (showNewAddressForm) {
        // If new address form is shown, validate and save it
        isValid = await saveNewAddress();
      } else if (selectedAddressId && selectedAddressId !== "") {
        // If using an existing address, just proceed
        isValid = true;
      } else {
        // No address selected or entered
        showNotification("Please select or add an address", "error");
        isValid = false;
      }

      if (isValid) {
        setStep(2);
        window.scrollTo(0, 0);
      }
    }
  };

  // Handle going back to previous step
  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
      window.scrollTo(0, 0);
    }
  };

  // Handle final order submission
  const handlePlaceOrder = async () => {
    // Prevent multiple clicks/submissions
    if (isProcessingPayment) {
      return;
    }

    // Add haptic feedback if available
    if ("vibrate" in navigator) {
      navigator.vibrate([40, 30, 100]);
    }

    try {
      // Validate that we have a valid address selected
      if (!selectedAddressId) {
        showNotification("Please select a delivery address", "error");
        setStep(1); // Go back to address selection
        return;
      }

      // Check if the selected address exists in saved addresses
      const addressExists = savedAddresses.some(addr => addr.id === selectedAddressId);
      if (!addressExists) {
        showNotification("Selected address is invalid. Please choose another address.", "error");
        setStep(1);
        return;
      }

      // Validate that we have a valid location selected - now required
      if (!selectedLocationId) {
        showNotification("Please select a delivery area", "error");
        setStep(1); // Go back to location selection

        // Scroll to the location section
        setTimeout(() => {
          const locationSection = document.querySelector('[data-section="delivery-locations"]');
          if (locationSection) {
            locationSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }, 300);
        return;
      }

      // Check if the selected location exists
      const locationExists = locations.some(loc => loc.id === selectedLocationId);
      if (!locationExists) {
        showNotification("Selected delivery area is invalid. Please choose another area.", "error");
        setStep(1);

        // Scroll to the location section
        setTimeout(() => {
          const locationSection = document.querySelector('[data-section="delivery-locations"]');
          if (locationSection) {
            locationSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }, 300);
        return;
      }

      // Prepare order items
      const orderItems = cartItems.map((item) => ({
        product_id: parseInt(item.id) || 0,
        product_name: item.name,
        product_price: item.price,
        quantity: item.quantity,
        total_price: item.price * item.quantity,
      }));

      // Prepare order data
      const orderData = {
        items: orderItems,
        address_id: selectedAddressId,
        location_id: selectedLocationId,
        payment_method: paymentMethod,
        total_amount: pricing.total,
        delivery_fee: pricing.deliveryFee,
        discount_amount: pricing.discount,
        coupon_code: pricing.couponCode || null,
      };

      // Show loading overlay only for online payments
      if (paymentMethod === "online" && paymentMethodSettings.online_payment_enabled) {
        setIsProcessingPayment(true);
      }

      // Submit the order
      showNotification("Creating your order...", "info");

      const response = await window.ApiClient.createOrder(orderData);

      if (!response.success) {
        throw new Error(response.message || response.error || "Failed to create order");
      }

      // Order created successfully
      const order = response.order;

      // Handle different payment methods
      if (paymentMethod === "online") {
        // For online payment, redirect to payment gateway
        showNotification("Redirecting to payment gateway...", "info");

        try {
          const paymentResponse = await window.ApiClient.initiatePayment(
            order.id
          );

          if (paymentResponse.success && paymentResponse.redirectUrl) {
            // Clear cart before redirecting to payment gateway
            if (typeof window !== "undefined" && window.CartUtils) {
              window.CartUtils.clearCart();
            }

            // Keep the loading overlay visible during redirect
            // The overlay will naturally disappear when the page unloads

            // Redirect to payment gateway
            window.location.href = paymentResponse.redirectUrl;
            return;
          } else {
            throw new Error(
              paymentResponse.message || "Payment initiation failed"
            );
          }
        } catch (paymentError) {
          console.error("Payment error:", paymentError);
          setIsProcessingPayment(false); // Hide loading overlay
          showNotification(
            "Payment initiation failed. Please try again.",
            "error"
          );
          return;
        }
      } else {
        // For cash on delivery, simply complete the order
        completeOrder(order);
      }
    } catch (error) {
      console.error("Order placement error:", error);
      // Hide loading overlay
      setIsProcessingPayment(false);

      // More descriptive error messages based on error type
      let errorMessage = error.message || "Failed to place order";

      if (errorMessage.includes("FOREIGN KEY constraint failed")) {
        errorMessage = "The selected address could not be used. Please select a different address.";
        setStep(1); // Go back to address selection
      } else if (errorMessage.includes("Authentication")) {
        errorMessage = "Your session has expired. Please log in again.";
        // Redirect to login after a short delay
        setTimeout(() => {
          window.location.href = "/login?redirect=/checkout";
        }, 2000);
      }

      showNotification(errorMessage, "error");
    }
  };

  // Complete the order after payment (or for COD)
  const completeOrder = (order) => {
    // Clear the cart
    if (typeof window !== "undefined" && window.CartUtils) {
      window.CartUtils.clearCart();
    }

    // Show success notification
    showNotification("Order placed successfully!", "success");

    // For COD orders, hide the loading overlay
    if (paymentMethod === "cash") {
      setIsProcessingPayment(false);
    }

    // Redirect to order confirmation page or orders list
    setTimeout(() => {
      window.location.href = `/orders?new_order=${order.id}`;
    }, 2000);
  };

  // Show notification with proper icon
  const showNotification = (message, type = "success") => {
    if (typeof window !== "undefined") {
      let toastContainer = document.querySelector(".toast-container");

      if (!toastContainer) {
        toastContainer = document.createElement("div");
        toastContainer.className =
          "toast-container fixed bottom-24 sm:bottom-20 left-0 right-0 flex flex-col items-center z-50 pointer-events-none px-4";
        document.body.appendChild(toastContainer);
      }

      const toast = document.createElement("div");
      toast.className =
        "bg-gray-800 text-white px-4 py-3 rounded-xl text-sm font-medium shadow-lg opacity-0 transition-all duration-300 transform translate-y-4 mb-2 flex items-center max-w-md w-full justify-center";
      const icon = document.createElement("span");
      icon.className = "material-icons-round mr-2 text-[16px] md:text-[18px]";

      if (type === "error") {
        icon.textContent = "error";
      } else if (message.includes("success")) {
        icon.textContent = "check_circle";
      } else if (message.includes("Redirect")) {
        icon.textContent = "payments";
      } else {
        icon.textContent = "info";
      }

      toast.appendChild(icon);

      const textSpan = document.createElement("span");
      textSpan.textContent = message;
      toast.appendChild(textSpan);

      toastContainer.appendChild(toast);

      setTimeout(() => {
        toast.classList.remove("opacity-0", "translate-y-4");
        toast.classList.add("opacity-95");
      }, 10);

      setTimeout(() => {
        toast.classList.add("opacity-0", "translate-y-4");
        setTimeout(() => {
          toast.remove();
        }, 300);
      }, 2500);
    }
  };

  // Add effect to restore form data after login
  useEffect(() => {
    // Check if there's pending address data from before login
    if (
      typeof window !== "undefined" &&
      sessionStorage.getItem("pendingAddressData")
    ) {
      try {
        const savedData = JSON.parse(
          sessionStorage.getItem("pendingAddressData")
        );
        if (savedData) {
          setShippingInfo(savedData);
          setShowNewAddressForm(true);
          // Clear the saved data
          sessionStorage.removeItem("pendingAddressData");
        }
      } catch (e) {
        console.error("Failed to restore address data:", e);
      }
    }
  }, []);

  // Render progress steps with improved design
  const renderProgressSteps = () => (
    <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4 mb-6 overflow-hidden relative">
      {/* Decorative gradient background */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-orange-50 to-transparent rounded-bl-full -z-10"></div>

      <div className="flex items-center justify-between">
        <div className="flex flex-col items-center">
          <div
            className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ${
              step >= 1
                ? "bg-[#5466F7] text-white shadow-md"
                : "bg-gray-100 text-gray-400"
            }`}
          >
            <span className="material-icons-round">location_on</span>
          </div>
          <span
            className={`text-xs mt-2 font-medium ${
              step >= 1 ? "text-gray-800" : "text-gray-400"
            }`}
          >
            Shipping
          </span>
        </div>

        <div
          className={`flex-1 h-1 mx-4 rounded-full transition-all duration-300 ${
            step >= 2 ? "bg-[#5466F7]" : "bg-gray-200"
          }`}
        ></div>

        <div className="flex flex-col items-center">
          <div
            className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ${
              step >= 2
                ? "bg-[#5466F7] text-white shadow-md"
                : "bg-gray-100 text-gray-400"
            }`}
          >
            <span className="material-icons-round">fact_check</span>
          </div>
          <span
            className={`text-xs mt-2 font-medium ${
              step >= 2 ? "text-gray-800" : "text-gray-400"
            }`}
          >
            Review
          </span>
        </div>
      </div>
    </div>
  );

  // Render shipping/address selection with improved design
  const renderAddressSelection = () => (
    <div className="space-y-5">
      {/* Delivery Locations Section - Now the primary place to select location */}
      <div data-section="delivery-locations" className="bg-white rounded-xl shadow-md border border-gray-100 p-5 mb-4 overflow-hidden relative">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-green-50 to-transparent rounded-bl-full -z-10"></div>
        <div className="flex items-center justify-between mb-5">
          <h2 className="text-xl font-semibold text-gray-800 flex items-center">
            <span className="material-icons-round mr-3 text-[#5466F7]">
              local_shipping
            </span>
            Select Delivery Area
          </h2>
          <span className="text-xs bg-blue-100 text-[#5466F7] px-2 py-1 rounded-full">
            Required
          </span>
        </div>
        <p className="text-gray-600 mb-4">
          Choose your delivery area to see accurate delivery fees and availability.
        </p>

        {loadingLocations ? (
          <div className="flex flex-col items-center justify-center py-10">
            <div className="w-14 h-14 border-4 border-gray-100 border-t-[#5466F7] border-b-[#5466F7] rounded-full animate-spin mb-5"></div>
            <p className="text-gray-600 font-medium">
              Loading available delivery areas...
            </p>
          </div>
        ) : locations.length > 0 ? (
          <div className="space-y-3">
            {locations.map((location) => (
              <div
                key={location.id}
                className={`border rounded-xl p-4 cursor-pointer transition-all hover:shadow-md ${
                  selectedLocationId === location.id
                    ? "border-[#5466F7] bg-blue-50 shadow-sm"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => {
                  try {
                    setSelectedLocationId(location.id);
                    updatePricingWithLocation(location.id);
                    // Show a notification for better user feedback
                    showNotification(`Selected delivery area: ${location.name}`);
                  } catch (error) {
                    console.error('Error selecting location:', error);
                    // Still update the UI state even if there's an error
                    setSelectedLocationId(location.id);
                    showNotification('Selected delivery area, but pricing may not be accurate', 'warning');
                  }
                }}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    <div
                      className={`w-5 h-5 rounded-full border flex-shrink-0 flex items-center justify-center ${
                        selectedLocationId === location.id
                          ? "border-[#5466F7] border-2 bg-white"
                          : "border-gray-300"
                      }`}
                    >
                      {selectedLocationId === location.id && (
                        <div className="w-2.5 h-2.5 rounded-full bg-[#5466F7]"></div>
                      )}
                    </div>

                    <div className="ml-4">
                      <p className="font-semibold text-gray-800">
                        {location.name}
                      </p>
                      <p className="text-gray-600 text-sm mt-1">
                        {location.address}
                      </p>
                      {location.delivery_fee !== undefined && (
                        <p className="text-xs text-green-600 mt-1 flex items-center">
                          <span className="material-icons-round text-xs mr-1">local_shipping</span>
                          Delivery Fee: ₹{parseFloat(location.delivery_fee || 0).toFixed(2)}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <div className="w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mb-5">
              <span className="material-icons-round text-[#5466F7] text-2xl">
                location_off
              </span>
            </div>
            <p className="text-gray-500 max-w-xs">
              No delivery areas available. Please try again later.
            </p>
          </div>
        )}

        {/* Show warning if no location is selected */}
        {!loadingLocations && locations.length > 0 && !selectedLocationId && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-100 rounded-lg text-yellow-700 text-sm flex items-center">
            <span className="material-icons-round text-sm mr-1.5">
              warning
            </span>
            <span>
              Please select a delivery area to continue with your order.
            </span>
          </div>
        )}
      </div>

      {/* Saved Addresses Section */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-5 mb-4 overflow-hidden relative">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-orange-50 to-transparent rounded-bl-full -z-10"></div>
        <h2 className="text-xl font-semibold text-gray-800 mb-5 flex items-center">
          <span className="material-icons-round mr-3 text-[#5466F7]">
            location_on
          </span>
          Delivery Address
        </h2>

        {loadingAddresses ? (
          <div className="flex flex-col items-center justify-center py-10">
            <div className="w-14 h-14 border-4 border-gray-100 border-t-[#5466F7] border-b-[#5466F7] rounded-full animate-spin mb-5"></div>
            <p className="text-gray-600 font-medium">
              Loading your addresses...
            </p>
          </div>
        ) : savedAddresses.length > 0 ? (
          <div className="space-y-3">
            {savedAddresses.map((address) => (
              <div
                key={address.id}
                className={`border rounded-xl p-4 cursor-pointer transition-all hover:shadow-md ${
                  selectedAddressId === address.id
                    ? "border-[#5466F7] bg-blue-50 shadow-sm"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => selectAddress(address.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    <div
                      className={`w-5 h-5 rounded-full border flex-shrink-0 flex items-center justify-center ${
                        selectedAddressId === address.id
                          ? "border-[#5466F7] border-2 bg-white"
                          : "border-gray-300"
                      }`}
                    >
                      {selectedAddressId === address.id && (
                        <div className="w-2.5 h-2.5 rounded-full bg-[#5466F7]"></div>
                      )}
                    </div>

                    <div className="ml-4">
                      <div className="flex items-center">
                        <p className="font-semibold text-gray-800">
                          {address.full_name}
                        </p>
                        {address.is_default ? (
                          <span className="ml-2 text-xs bg-blue-100 text-[#5466F7] px-2 py-0.5 rounded-full font-medium">
                            Default
                          </span>
                        ) : null}
                      </div>
                      <p className="text-gray-500 text-sm mt-1">
                        {address.phone}
                      </p>
                      <p className="text-gray-600 text-sm mt-1">
                        {address.address}, {address.city}, {address.zip_code}
                      </p>
                      {address.instructions && (
                        <p className="text-gray-500 text-xs mt-2 bg-gray-50 p-2 rounded-lg">
                          <span className="font-medium">Note:</span>{" "}
                          {address.instructions}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}

            <button
              onClick={() => {
                setShowNewAddressForm(true);
                setSelectedAddressId(null);
                // Reset form with empty fields
                setShippingInfo({
                  fullName: "",
                  phone: "",
                  address: "",
                  city: "",
                  zipCode: "",
                  instructions: "",
                  saveAddress: true,
                  setAsDefault: false,
                });
              }}
              className="w-full py-4 mt-3 border border-dashed border-gray-300 rounded-xl flex items-center justify-center text-[#5466F7] hover:bg-blue-50 transition-colors font-medium"
            >
              <span className="material-icons-round mr-2">
                add_circle_outline
              </span>
              Add New Address
            </button>
          </div>
        ) : (
          <div>
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <div className="w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mb-5">
                <span className="material-icons-round text-[#5466F7] text-2xl">
                  location_off
                </span>
              </div>
              <p className="text-gray-500 max-w-xs">
                No saved addresses found. Add your first address below.
              </p>
            </div>
            {!showNewAddressForm && (
              <button
                onClick={() => setShowNewAddressForm(true)}
                className="w-full py-4 mt-3 bg-[#5466F7] text-white rounded-xl flex items-center justify-center hover:bg-[#4555e2] transition-all shadow-sm hover:shadow"
              >
                <span className="material-icons-round mr-2">add_location</span>
                Add New Address
              </button>
            )}
          </div>
        )}
      </div>

      {/* Show the form conditionally */}
      {showNewAddressForm && renderShippingForm()}
    </div>
  );

  // Render shipping form with improved design
  const renderShippingForm = () => (
    <div className="bg-white rounded-xl shadow-md border border-gray-100 p-5 mb-6 overflow-hidden relative">
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-blue-50 to-transparent rounded-bl-full -z-10"></div>
      <div className="flex justify-between items-center mb-5">
        <h2 className="text-xl font-semibold text-gray-800 flex items-center">
          <span className="material-icons-round mr-3 text-[#5466F7]">
            add_location_alt
          </span>
          New Address
        </h2>
        {savedAddresses.length > 0 && (
          <button
            onClick={() => {
              // Select the previous address if there was one
              console.log("🚀 ~ Checkout ~ savedAddresses:", savedAddresses);
              if (savedAddresses.length > 0) {
                const defaultAddress =
                  savedAddresses.find((addr) => addr.is_default) ||
                  savedAddresses[0];
                setSelectedAddressId(defaultAddress.id);
                setShippingInfo({
                  fullName: defaultAddress.full_name,
                  phone: defaultAddress.phone,
                  address: defaultAddress.address,
                  city: defaultAddress.city,
                  zipCode: defaultAddress.zip_code,
                  instructions: defaultAddress.instructions || "",
                  saveAddress: false,
                  setAsDefault: false,
                });
              }
              setShowNewAddressForm(false);
            }}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <span className="material-icons-round text-gray-500">close</span>
          </button>
        )}
      </div>

      <div className="space-y-4">
        {/* Full Name Field */}
        <div>
          <label
            htmlFor="fullName"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Full Name
          </label>
          <div
            className={`relative rounded-xl overflow-hidden transition-all ${
              shippingErrors.fullName ? "ring-2 ring-red-500" : ""
            }`}
          >
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <span className="material-icons-round text-gray-400">person</span>
            </div>
            <input
              type="text"
              id="fullName"
              name="fullName"
              value={shippingInfo.fullName}
              onChange={handleShippingChange}
              className="w-full pl-10 pr-4 py-3.5 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#5466F7] transition-shadow"
              placeholder="Enter your full name"
            />
          </div>
          {shippingErrors.fullName && (
            <p className="text-red-500 text-sm mt-2 flex items-center">
              <span className="material-icons-round text-sm mr-1">error</span>
              {shippingErrors.fullName}
            </p>
          )}
        </div>

        {/* Phone Number Field */}
        <div>
          <label
            htmlFor="phone"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Phone Number
          </label>
          <div
            className={`relative rounded-xl overflow-hidden transition-all ${
              shippingErrors.phone ? "ring-2 ring-red-500" : ""
            }`}
          >
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <span className="material-icons-round text-gray-400">phone</span>
            </div>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={shippingInfo.phone}
              onChange={handleShippingChange}
              className="w-full pl-10 pr-4 py-3.5 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#5466F7] transition-shadow"
              placeholder="Enter your phone number"
            />
          </div>
          {shippingErrors.phone && (
            <p className="text-red-500 text-sm mt-2 flex items-center">
              <span className="material-icons-round text-sm mr-1">error</span>
              {shippingErrors.phone}
            </p>
          )}
        </div>

        {/* Address Field */}
        <div>
          <label
            htmlFor="address"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Address
          </label>
          <div
            className={`relative rounded-xl overflow-hidden transition-all ${
              shippingErrors.address ? "ring-2 ring-red-500" : ""
            }`}
          >
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <span className="material-icons-round text-gray-400">home</span>
            </div>
            <input
              type="text"
              id="address"
              name="address"
              value={shippingInfo.address}
              onChange={handleShippingChange}
              className="w-full pl-10 pr-4 py-3.5 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#5466F7] transition-shadow"
              placeholder="Enter your address"
            />
          </div>
          {shippingErrors.address && (
            <p className="text-red-500 text-sm mt-2 flex items-center">
              <span className="material-icons-round text-sm mr-1">error</span>
              {shippingErrors.address}
            </p>
          )}
        </div>

        {/* City & ZIP Code Fields */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label
              htmlFor="city"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              City
            </label>
            <div
              className={`relative rounded-xl overflow-hidden transition-all ${
                shippingErrors.city ? "ring-2 ring-red-500" : ""
              }`}
            >
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <span className="material-icons-round text-gray-400">
                  location_city
                </span>
              </div>
              <input
                type="text"
                id="city"
                name="city"
                value={shippingInfo.city}
                onChange={handleShippingChange}
                className="w-full pl-10 pr-4 py-3.5 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#5466F7] transition-shadow"
                placeholder="City"
              />
            </div>
            {shippingErrors.city && (
              <p className="text-red-500 text-sm mt-2 flex items-center">
                <span className="material-icons-round text-sm mr-1">error</span>
                {shippingErrors.city}
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="zipCode"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              ZIP Code
            </label>
            <div
              className={`relative rounded-xl overflow-hidden transition-all ${
                shippingErrors.zipCode ? "ring-2 ring-red-500" : ""
              }`}
            >
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <span className="material-icons-round text-gray-400">pin</span>
              </div>
              <input
                type="text"
                id="zipCode"
                name="zipCode"
                value={shippingInfo.zipCode}
                onChange={handleShippingChange}
                className="w-full pl-10 pr-4 py-3.5 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#5466F7] transition-shadow"
                placeholder="ZIP Code"
              />
            </div>
            {shippingErrors.zipCode && (
              <p className="text-red-500 text-sm mt-2 flex items-center">
                <span className="material-icons-round text-sm mr-1">error</span>
                {shippingErrors.zipCode}
              </p>
            )}
          </div>
        </div>

        {/* Delivery Instructions */}
        <div>
          <label
            htmlFor="instructions"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Delivery Instructions (Optional)
          </label>
          <div className="relative rounded-xl overflow-hidden focus-within:ring-2 focus-within:ring-[#5466F7] transition-all">
            <div className="absolute top-3 left-3 flex items-start pointer-events-none">
              <span className="material-icons-round text-gray-400">note</span>
            </div>
            <textarea
              id="instructions"
              name="instructions"
              value={shippingInfo.instructions}
              onChange={handleShippingChange}
              rows="3"
              className="w-full pl-10 pr-4 py-3.5 border border-gray-200 rounded-xl focus:outline-none"
              placeholder="Any special instructions for delivery"
            ></textarea>
          </div>
        </div>

        {/* Address Options */}
        {!selectedAddressId && (
          <div className="space-y-3 pt-3 pb-1">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="saveAddress"
                name="saveAddress"
                checked={shippingInfo.saveAddress}
                onChange={handleShippingChange}
                className="h-4 w-4 text-[#5466F7] focus:ring-[#5466F7] rounded"
              />
              <label
                htmlFor="saveAddress"
                className="ml-2 text-sm text-gray-700"
              >
                Save this address for future orders
              </label>
            </div>

            {shippingInfo.saveAddress && (
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="setAsDefault"
                  name="setAsDefault"
                  checked={shippingInfo.setAsDefault}
                  onChange={handleShippingChange}
                  className="h-4 w-4 text-[#5466F7] focus:ring-[#5466F7] rounded"
                />
                <label
                  htmlFor="setAsDefault"
                  className="ml-2 text-sm text-gray-700"
                >
                  Set as default address
                </label>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );

  // Render order review with improved design
  const renderOrderReview = () => (
    <div className="space-y-5">
      {/* Order Summary */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-5 overflow-hidden relative">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-blue-50 to-transparent rounded-bl-full -z-10"></div>
        <h2 className="text-xl font-semibold text-gray-800 mb-5 flex items-center">
          <span className="material-icons-round mr-3 text-[#5466F7]">
            shopping_bag
          </span>
          Order Summary
        </h2>
        <div className="divide-y divide-gray-100 overflow-hidden">
          {cartItems.map((item) => (
            <div key={item.id} className="flex py-3">
                        <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 border border-gray-200 bg-gray-50">
                          <img
                            src={item.image}
                            alt={item.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="ml-3 flex-1 flex flex-col">
                          <div className="flex justify-between">
                            <div>
                              <p className="font-medium text-gray-800 line-clamp-1">
                                {item.name}
                              </p>
                              {item.unit_type && item.unit_value && (
                                <div className="text-xs text-blue-600">
                                  {item.unit_type === 'quantity'
                                    ? `${item.unit_value} ${item.unit_value > 1 ? 'items' : 'item'}`
                                    : `${item.unit_value}${item.unit_type}`}
                                </div>
                              )}
                              <p className="text-sm text-gray-500 mt-0.5">
                                {formatCurrency(item.price)} x {item.quantity}
                              </p>
                            </div>
                            <span className="font-semibold">
                              {formatCurrency(item.price * item.quantity)}
                            </span>
                          </div>
                        </div>
                      </div>
          ))}
        </div>
      </div>

      {/* Delivery Area - Only show if locations exist and one is selected */}
      {locations.length > 0 && selectedLocationId && (
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-5 overflow-hidden relative">
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-green-50 to-transparent rounded-bl-full -z-10"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-5 flex items-center">
            <span className="material-icons-round mr-3 text-[#5466F7]">
              local_shipping
            </span>
            Delivery Area
          </h2>
          <div className="bg-gray-50 p-4 rounded-xl flex flex-col space-y-2">
            <div className="flex flex-col">
              <span className="w-24 text-gray-500 flex-shrink-0">Area:</span>
              <span className="font-medium text-gray-800">
                {locations.find(loc => loc.id === selectedLocationId)?.name || 'Unknown area'}
              </span>
            </div>
            <div className="flex flex-col">
              <span className="w-24 text-gray-500 flex-shrink-0 self-start">
                Details:
              </span>
              <span className="font-medium text-gray-800">
                {locations.find(loc => loc.id === selectedLocationId)?.address || 'Details not available'}
              </span>
            </div>
          </div>
          <a
            href="#"
            onClick={(e) => {
              e.preventDefault();
              setStep(1);
            }}
            className="text-[#5466F7] text-sm font-medium mt-3 flex items-center"
          >
            <span className="material-icons-round text-sm mr-1">edit</span>
            Change delivery area
          </a>
        </div>
      )}

      {/* Shipping Details */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-5 overflow-hidden relative">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-orange-50 to-transparent rounded-bl-full -z-10"></div>
        <h2 className="text-xl font-semibold text-gray-800 mb-5 flex items-center">
          <span className="material-icons-round mr-3 text-[#5466F7]">
            local_shipping
          </span>
          Delivery Address
        </h2>
        <div className="bg-gray-50 p-4 rounded-xl flex flex-col space-y-2">
          <div className="flex flex-col">
            <span className="w-24 text-gray-500 flex-shrink-0">Full Name:</span>
            <span className="font-medium text-gray-800">
              {shippingInfo.fullName}
            </span>
          </div>
          <div className="flex flex-col">
            <span className="w-24 text-gray-500 flex-shrink-0">Phone:</span>
            <span className="font-medium text-gray-800">
              {shippingInfo.phone}
            </span>
          </div>
          <div className="flex flex-col">
            <span className="w-24 text-gray-500 flex-shrink-0 self-start">
              Address:
            </span>
            <span className="font-medium text-gray-800">
              {shippingInfo.address}, {shippingInfo.city},{" "}
              {shippingInfo.zipCode}
            </span>
          </div>
          {shippingInfo.instructions && (
            <div className="flex mt-1 pt-2 border-t border-gray-200 flex-col">
              <span className="w-24 text-gray-500 flex-shrink-0 self-start">
                Note:
              </span>
              <span className="text-gray-700">{shippingInfo.instructions}</span>
            </div>
          )}
        </div>
        <a
          href="#"
          onClick={(e) => {
            e.preventDefault();
            setStep(1);
          }}
          className="text-[#5466F7] text-sm font-medium mt-3 flex items-center"
        >
          <span className="material-icons-round text-sm mr-1">edit</span>
          Change address
        </a>
      </div>

      {/* Payment Method */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-5 overflow-hidden relative">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-green-50 to-transparent rounded-bl-full -z-10"></div>
        <h2 className="text-xl font-semibold text-gray-800 mb-5 flex items-center">
          <span className="material-icons-round mr-3 text-[#5466F7]">
            payment
          </span>
          Payment Method
        </h2>

        {/* No payment methods available */}
        {!paymentMethodSettings.online_payment_enabled && !paymentMethodSettings.cash_on_delivery_enabled && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-800">
            <div className="flex items-center">
              <span className="material-icons-round mr-2 text-yellow-600">warning</span>
              <p className="font-medium">Payment methods unavailable</p>
            </div>
            <p className="mt-1 text-sm">
              All payment methods are currently unavailable. Please try again later.
            </p>
          </div>
        )}

        {/* At least one payment method is available */}
        {(paymentMethodSettings.online_payment_enabled || paymentMethodSettings.cash_on_delivery_enabled) && (
          <div className="flex flex-col space-y-3">
            {/* Online Payment Option */}
            {paymentMethodSettings.online_payment_enabled && (
              <div
                className={`border rounded-xl p-5 transition-all cursor-pointer ${
                  paymentMethod === "online"
                    ? "border-[#5466F7] bg-blue-50 shadow"
                    : "border-gray-200 hover:border-blue-200"
                }`}
                onClick={() => setPaymentMethod("online")}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div
                      className={`flex-shrink-0 w-10 h-10 rounded-full ${
                        paymentMethod === "online"
                          ? "bg-[#5466F7] text-white"
                          : "bg-gray-100 text-gray-400"
                      } flex items-center justify-center`}
                    >
                      <span className="material-icons-round">credit_card</span>
                    </div>
                    <div className="ml-4">
                      <h3 className="font-semibold text-gray-800">
                        Online Payment
                      </h3>
                      <p className="text-xs text-gray-500 mt-1">
                        Pay securely online via card/UPI
                      </p>
                    </div>
                  </div>
                  <div
                    className={`w-5 h-5 rounded-full border ${
                      paymentMethod === "online"
                        ? "border-[#5466F7] border-2"
                        : "border-gray-300"
                    } flex items-center justify-center`}
                  >
                    {paymentMethod === "online" && (
                      <div className="w-2 h-2 rounded-full bg-[#5466F7]"></div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Cash on Delivery Option */}
            {paymentMethodSettings.cash_on_delivery_enabled && (
              <div
                className={`border rounded-xl p-5 transition-all cursor-pointer ${
                  paymentMethod === "cash"
                    ? "border-[#5466F7] bg-blue-50 shadow"
                    : "border-gray-200 hover:border-blue-200"
                }`}
                onClick={() => setPaymentMethod("cash")}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div
                      className={`flex-shrink-0 w-10 h-10 rounded-full ${
                        paymentMethod === "cash"
                          ? "bg-[#5466F7] text-white"
                          : "bg-gray-100 text-gray-400"
                      } flex items-center justify-center`}
                    >
                      <span className="material-icons-round">payments</span>
                    </div>
                    <div className="ml-4">
                      <h3 className="font-semibold text-gray-800">
                        Cash on Delivery
                      </h3>
                      <p className="text-xs text-gray-500 mt-1">
                        Pay when order arrives
                      </p>
                    </div>
                  </div>
                  <div
                    className={`w-5 h-5 rounded-full border ${
                      paymentMethod === "cash"
                        ? "border-[#5466F7] border-2"
                        : "border-gray-300"
                    } flex items-center justify-center`}
                  >
                    {paymentMethod === "cash" && (
                      <div className="w-2 h-2 rounded-full bg-[#5466F7]"></div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Price Details */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-5 overflow-hidden relative">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-blue-50 to-transparent rounded-bl-full -z-10"></div>
        <h2 className="text-xl font-semibold text-gray-800 mb-5 flex items-center">
          <span className="material-icons-round mr-3 text-[#5466F7]">
            receipt_long
          </span>
          Price Details
        </h2>
        <div className="space-y-3">
          <div className="flex justify-between py-1.5">
            <span className="text-gray-500">Subtotal</span>
            <span className="font-medium text-gray-800">
              ₹ {pricing.subtotal.toFixed(2)}
            </span>
          </div>
          <div className="flex justify-between py-1.5">
            <span className="text-gray-500">Delivery Fee</span>
            <span className="font-medium">
              {pricing.deliveryFee > 0 ? (
                <span className="text-gray-800">
                  ₹ {pricing.deliveryFee.toFixed(2)}
                </span>
              ) : (
                <span className="text-green-600">Free</span>
              )}
            </span>
          </div>
          {pricing.discount > 0 && (
            <div className="flex justify-between text-green-600 py-1.5">
              <span className="flex items-center">
                <span className="material-icons-round text-sm mr-1.5">
                  discount
                </span>
                <span>
                  {couponInfo ? `Discount (${couponInfo.code})` : "Discount"}
                </span>
              </span>
              <span className="font-medium">
                -₹ {pricing.discount.toFixed(2)}
              </span>
            </div>
          )}
          <div className="border-t border-gray-100 pt-3 mt-3">
            <div className="flex justify-between items-center">
              <span className="font-semibold text-gray-800">Total</span>
              <div>
                <span className="text-2xl font-bold text-[#5466F7]">
                  ₹ {pricing.total.toFixed(2)}
                </span>
              </div>
            </div>

            {/* Free Delivery Notification */}
            {pricing.isFreeDelivery && (
              <div className="mt-4 p-2.5 bg-green-50 border border-green-100 rounded-lg text-green-700 text-sm flex items-center">
                <span className="material-icons-round text-sm mr-1.5">
                  local_shipping
                </span>
                <span>Free delivery applied!</span>
              </div>
            )}

            {/* Free Delivery Threshold Notification */}
            {!pricing.isFreeDelivery && pricing.freeDeliveryThreshold > 0 && (
              <div className="mt-4 p-2.5 bg-blue-50 border border-blue-100 rounded-lg text-blue-700 text-sm flex items-center">
                <span className="material-icons-round text-sm mr-1.5">
                  info
                </span>
                <span>
                  Add ₹{(pricing.freeDeliveryThreshold - pricing.subtotal).toFixed(2)} more to get free delivery!
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="pb-24 sm:pb-20 max-w-xl mx-auto">
      {/* Payment Processing Overlay - Only shown for online payments */}
      <LoadingOverlay
        isVisible={isProcessingPayment && paymentMethod === "online"}
        message="Processing your payment..."
        spinnerSize="lg"
        spinnerColor="blue"
        icon="payments"
      />

      {/* Initial Loading State */}
      {isLoading ? (
        <div className="flex flex-col items-center justify-center py-20">
          <div className="w-16 h-16 border-4 border-gray-100 border-t-[#5466F7] border-b-[#5466F7] rounded-full animate-spin mb-5"></div>
          <p className="text-gray-600 font-medium">
            Loading your order details...
          </p>
        </div>
      ) : (
        <>
          {/* Empty Cart State */}
          {cartItems.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
              <div className="w-24 h-24 bg-blue-50 rounded-full flex items-center justify-center mb-6 border border-blue-100 shadow-inner">
                <span className="material-icons-round text-[#5466F7] text-[40px]">
                  shopping_cart
                </span>
              </div>
              <h2 className="text-xl font-semibold text-gray-800 mb-3">
                Your cart is empty
              </h2>
              <p className="text-gray-500 mb-8 max-w-xs">
                Looks like you haven't added any items to your cart yet. Start
                browsing our educational materials!
              </p>
              <a
                href="/"
                className="bg-[#5466F7] text-white px-8 py-3.5 rounded-xl font-medium hover:bg-[#4555e2] transition-all flex items-center justify-center shadow-sm hover:shadow"
              >
                <span className="material-icons-round mr-2">
                  school
                </span>
                Browse Materials
              </a>
            </div>
          ) : (
            <div className="px-4">
              {renderProgressSteps()}
              {step === 1 && renderAddressSelection()}
              {step === 2 && renderOrderReview()}

              {/* Action Buttons */}
              <div className="flex flex-col gap-4 mt-6 mb-10">
                {step > 1 && (
                  <button
                    onClick={handleBack}
                    className="flex-1 py-4 border border-gray-200 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-all flex items-center justify-center shadow-sm"
                  >
                    <span className="material-icons-round mr-2">
                      arrow_back
                    </span>
                    Back
                  </button>
                )}

                {step < 2 ? (
                  <button
                    onClick={handleContinue}
                    className="flex-1 py-4 bg-[#5466F7] text-white rounded-xl font-medium hover:bg-[#4555e2] transition-all flex items-center justify-center shadow-sm hover:shadow"
                  >
                    Continue
                    <span className="material-icons-round ml-2">
                      arrow_forward
                    </span>
                  </button>
                ) : (
                  <button
                    onClick={handlePlaceOrder}
                    disabled={isProcessingPayment}
                    className={`flex-1 py-4 rounded-xl font-semibold transition-all flex items-center justify-center shadow-md ${
                      isProcessingPayment
                        ? "bg-gray-400 text-white cursor-not-allowed"
                        : "bg-[#5466F7] text-white hover:bg-[#4555e2] hover:shadow-lg"
                    }`}
                  >
                    <span className="material-icons-round mr-2">
                      {paymentMethod === "online" ? "payments" : "check_circle"}
                    </span>
                    {isProcessingPayment
                      ? "Processing..."
                      : paymentMethod === "online"
                        ? "Proceed to Payment"
                        : "Place Order"}
                  </button>
                )}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
