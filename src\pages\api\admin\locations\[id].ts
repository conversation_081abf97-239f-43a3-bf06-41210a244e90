import type { APIRoute } from 'astro';
import { adminAuthMiddleware } from '../../../../middleware/auth';
import { getOrderLocationById, updateOrderLocation, deleteOrderLocation } from '../../../../db/database';

export const prerender = false;

/**
 * Get a specific order location (admin)
 */
export const GET: APIRoute = async ({ request, params, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get location ID from URL params
    const locationId = parseInt(params.id);
    
    if (isNaN(locationId)) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Invalid location ID' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get location
    const location = await getOrderLocationById(locals.runtime.env, locationId);
    
    if (!location) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Location not found' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({ 
      success: true,
      location 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error fetching location:', error);
    
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Failed to fetch location' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

/**
 * Update a specific order location (admin)
 */
export const PATCH: APIRoute = async ({ request, params, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get location ID from URL params
    const locationId = parseInt(params.id);
    
    if (isNaN(locationId)) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Invalid location ID' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get existing location
    const existingLocation = await getOrderLocationById(locals.runtime.env, locationId);
    
    if (!existingLocation) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Location not found' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Parse request body
    const data = await request.json();
    
    // Update location
    const updatedLocation = await updateOrderLocation(locals.runtime.env, {
      id: locationId,
      name: data.name || existingLocation.name,
      address: data.address || existingLocation.address,
      is_active: data.is_active !== undefined ? data.is_active : existingLocation.is_active,
      created_at: existingLocation.created_at
    });

    if (!updatedLocation) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Failed to update location' 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({ 
      success: true,
      location: updatedLocation 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error updating location:', error);
    
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Failed to update location' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

/**
 * Delete a specific order location (admin)
 */
export const DELETE: APIRoute = async ({ request, params, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get location ID from URL params
    const locationId = parseInt(params.id);
    
    if (isNaN(locationId)) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Invalid location ID' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Delete location
    const success = await deleteOrderLocation(locals.runtime.env, locationId);
    
    if (!success) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Failed to delete location' 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({ 
      success: true,
      message: 'Location deleted successfully' 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error deleting location:', error);
    
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Failed to delete location' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
