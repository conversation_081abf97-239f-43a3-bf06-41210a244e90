import React, { useState, useEffect } from 'react';
import StatsCard from './StatsCard';
import DataTable from './DataTable';

export default function Dashboard() {
  const [isLoading, setIsLoading] = useState(true);
  const [period, setPeriod] = useState('week');
  const [category, setCategory] = useState('');
  const [stockFilter, setStockFilter] = useState('all');
  const [stats, setStats] = useState({
    totalOrders: 0,
    totalRevenue: 0,
    pendingOrders: 0,
    completedOrders: 0
  });
  const [recentOrders, setRecentOrders] = useState([]);
  const [stockRequirements, setStockRequirements] = useState([]);
  const [dailyStats, setDailyStats] = useState([]);
  const [categories, setCategories] = useState([]);
  const [error, setError] = useState(null);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories');
        if (response.ok) {
          const data = await response.json();
          setCategories(data.categories || []);
        }
      } catch (err) {
        console.error('Error fetching categories:', err);
      }
    };

    // fetchCategories();
  }, []);

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Get dashboard statistics from the API
        const response = await window.ApiClient.getAdminDashboardStats(period, category, stockFilter);

        if (response.success) {
          setStats({
            totalOrders: response.stats.totalOrders || 0,
            totalRevenue: response.stats.totalRevenue || 0,
            pendingOrders: response.stats.pendingOrders || 0,
            completedOrders: response.stats.completedOrders || 0
          });

          // Set stock requirements
          setStockRequirements(response.stats.stockRequirements.map(product => ({
            id: product.id,
            name: product.name,
            category: product.category_name,
            image: getCategoryEmoji(product.category_name),
            price: product.price,
            currentStock: product.currentStock || 0,
            pendingQuantity: product.pendingQuantity || 0,
            stockStatus: product.stockStatus,
            stockRequired: product.stockRequired || 0,
            unitType: product.unit_type || 'quantity',
            unitValue: product.unit_value || 1,
            totalUnitQuantity: product.totalUnitQuantity || 0
          })));

          // Set daily stats for chart
          setDailyStats(response.stats.dailyStats || []);

          // Get recent orders
          const ordersResponse = await window.ApiClient.getAdminOrders(1, 5);
          setRecentOrders(ordersResponse.orders || []);
        }
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [period, category, stockFilter]);

  // Get an emoji for a study material category
  const getCategoryEmoji = (category) => {
    const emojis = {
      '6th Grade': '📚',
      '7th Grade': '📖',
      '8th Grade': '📝',
      '9th Grade': '📊',
      '10th Grade': '🎓',
      'Telugu Medium': '🇮🇳',
      'English Medium': '🇬🇧',
      'Mathematics': '🔢'
    };

    return emojis[category] || '📚';
  };

  // Format currency values
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format unit quantities (ml, kg, etc.)
  const formatUnitQuantity = (quantity, unitType) => {
    if (!quantity) return '0';

    // Convert to appropriate units with proper formatting
    if (unitType === 'ml' && quantity >= 1000) {
      return `${(quantity / 1000).toLocaleString(undefined, { maximumFractionDigits: 2 })} L`;
    } else if (unitType === 'g' && quantity >= 1000) {
      return `${(quantity / 1000).toLocaleString(undefined, { maximumFractionDigits: 2 })} kg`;
    } else if (unitType === 'ml') {
      return `${quantity.toLocaleString()} ml`;
    } else if (unitType === 'l') {
      return `${quantity.toLocaleString()} L`;
    } else if (unitType === 'g') {
      return `${quantity.toLocaleString()} g`;
    } else if (unitType === 'kg') {
      return `${quantity.toLocaleString()} kg`;
    } else {
      return quantity.toLocaleString();
    }
  };

  // Format dates
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Handle period change
  const handlePeriodChange = (e) => {
    setPeriod(e.target.value);
  };

  // Handle category change
  const handleCategoryChange = (e) => {
    setCategory(e.target.value);
  };

  // Handle stock filter change
  const handleStockFilterChange = (e) => {
    setStockFilter(e.target.value);
  };

  // Order table columns configuration
  const orderColumns = [
    { key: 'order_number', label: 'Order #' },
    { key: 'user_name', label: 'Customer' },
    { key: 'created_at', label: 'Date', render: (row) => formatDate(row.created_at) },
    { key: 'total_amount', label: 'Total', render: (row) => formatCurrency(row.total_amount) },
    {
      key: 'location',
      label: 'Delivery Location',
      render: (row) => (
        <span>
          {row.location_address ? row.location_address : 'N/A'}
        </span>
      )
    },
    {
      key: 'order_status',
      label: 'Status',
      render: (row) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          row.order_status === 'delivered' ? 'bg-green-100 text-green-800' :
          row.order_status === 'processing' ? 'bg-blue-100 text-blue-800' :
          row.order_status === 'shipped' ? 'bg-indigo-100 text-indigo-800' :
          row.order_status === 'cancelled' ? 'bg-red-100 text-red-800' :
          'bg-purple-100 text-purple-800'
        }`}>
          {row.order_status.charAt(0).toUpperCase() + row.order_status.slice(1)}
        </span>
      )
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      render: (row) => (
        <a
          href={`/admin/order/${row.id}`}
          className="text-orange-600 hover:text-orange-900 font-medium"
        >
          View
        </a>
      )
    }
  ];

  // Stock requirements table columns configuration
  const stockRequirementsColumns = [
    {
      key: 'image',
      label: '',
      sortable: false,
      render: (row) => (
        <span className="text-2xl">{row.image}</span>
      )
    },
    {
      key: 'name',
      label: 'Product',
      render: (row) => (
        <div>
          <div className="font-medium">{row.name}</div>
          <div className="text-xs text-gray-500">
            {row.unitType === 'quantity' ? 'Item' :
             row.unitType === 'ml' ? 'Volume (ml)' :
             row.unitType === 'l' ? 'Volume (L)' :
             row.unitType === 'g' ? 'Weight (g)' :
             row.unitType === 'kg' ? 'Weight (kg)' : 'Item'}
          </div>
        </div>
      )
    },
    { key: 'category', label: 'Category' },
    {
      key: 'currentStock',
      label: 'Current Stock',
      render: (row) => (
        <div className="flex items-center">
          <span className={`font-medium ${parseInt(row.currentStock) === 0 ? 'text-red-600' : 'text-gray-900'}`}>
            {row.currentStock.toLocaleString()}
            {row.unitType !== 'quantity' && (
              <span className="text-xs ml-1">× {row.unitValue} {row.unitType}</span>
            )}
          </span>
          {parseInt(row.currentStock) < 10 && parseInt(row.currentStock) > 0 && (
            <span className="ml-2 text-xs text-amber-600 bg-amber-50 px-1.5 py-0.5 rounded">Low</span>
          )}
          {parseInt(row.currentStock) <= 0 && (
            <span className="ml-2 text-xs text-red-600 bg-red-50 px-1.5 py-0.5 rounded">Out</span>
          )}
        </div>
      )
    },
    {
      key: 'pendingQuantity',
      label: 'Pending Orders',
      render: (row) => (
        <div>
          <span className="font-medium">{row.pendingQuantity.toLocaleString()}</span>
          {row.unitType !== 'quantity' && (
            <div className="text-xs text-gray-500">
              {formatUnitQuantity(row.totalUnitQuantity, row.unitType)}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'stockRequired',
      label: 'Stock Required',
      render: (row) => (
        <div className="flex items-center">
          <div>
            <span className={`font-medium ${parseInt(row.stockRequired) > 0 ? 'text-orange-600' : 'text-gray-500'}`}>
              {row.stockRequired > 0 ? row.stockRequired.toLocaleString() : '0'}
            </span>
            {row.stockRequired > 0 && row.unitType !== 'quantity' && (
              <div className="text-xs text-orange-600">
                {formatUnitQuantity(row.stockRequired * row.unitValue, row.unitType)}
              </div>
            )}
          </div>
        </div>
      )
    }
  ];

  // Get day names for daily chart
  const getDayLabels = () => {
    if (!dailyStats || dailyStats.length === 0) return [];

    return dailyStats.map(stat => {
      const date = new Date(stat.date);
      return date.toLocaleDateString('en-US', { weekday: 'short' });
    });
  };

  // Get order counts for daily chart
  const getDailyOrderCounts = () => {
    if (!dailyStats || dailyStats.length === 0) {
      return Array(7).fill(0);
    }

    return dailyStats.map(day => day.orderCount);
  };

  // Get max order count for chart scaling
  const getMaxOrderCount = () => {
    if (!dailyStats || dailyStats.length === 0) return 100;

    const counts = dailyStats.map(day => day.orderCount);
    return Math.max(...counts, 10); // At least 10 to avoid empty chart
  };

  // Calculate bar heights as percentage of max
  const calculateBarHeight = (value) => {
    const max = getMaxOrderCount();
    return max > 0 ? (value / max) * 100 : 0;
  };

  // Helper to safely parse dates from API response
  const formatChartDate = (dateStr, periodType) => {
    try {
      if (periodType === 'year') {
        // For yearly data, we get YYYY-MM format
        return new Date(`${dateStr}-01`).toLocaleDateString('en-US', { month: 'short' });
      } else {
        // For other periods, we expect YYYY-MM-DD format
        return new Date(dateStr).toLocaleDateString('en-US', {
          weekday: periodType === 'week' ? 'short' : undefined,
          month: periodType === 'month' ? 'short' : undefined,
          day: periodType !== 'year' ? 'numeric' : undefined
        });
      }
    } catch (e) {
      console.error("Date parsing error:", e, dateStr);
      return "Invalid date";
    }
  };

  // If there's an error, show error message
  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4 mb-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <span className="material-icons-round text-red-400">error</span>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Orders"
          value={stats.totalOrders.toLocaleString()}
          change="12"
          icon="shopping_bag"
          iconBgColor="bg-blue-50"
          iconColor="text-blue-500"
          isLoading={isLoading}
        />
        <StatsCard
          title="Total Revenue"
          value={formatCurrency(stats.totalRevenue)}
          change="8.5"
          icon="payments"
          iconBgColor="bg-green-50"
          iconColor="text-green-500"
          isLoading={isLoading}
        />
        <StatsCard
          title="Pending Orders"
          value={stats.pendingOrders.toLocaleString()}
          change="3"
          icon="pending"
          iconBgColor="bg-orange-50"
          iconColor="text-orange-500"
          isLoading={isLoading}
        />
        <StatsCard
          title="Delivered Orders"
          value={stats.completedOrders.toLocaleString()}
          change="9.1"
          icon="local_shipping"
          iconBgColor="bg-purple-50"
          iconColor="text-purple-500"
          isLoading={isLoading}
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Order Summary Chart */}
        {/* <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-medium text-gray-800">Order Summary</h3>
            <div>
              <select
                value={period}
                onChange={handlePeriodChange}
                className="border-gray-300 rounded-md text-sm focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="day">Last 24 Hours</option>
                <option value="week">Last 7 Days</option>
                <option value="month">Last 30 Days</option>
                <option value="year">Last Year</option>
              </select>
            </div>
          </div>

          {isLoading ? (
            <div className="h-64 flex items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
            </div>
          ) : (
            <div className="h-64 flex items-end space-x-2">
              {dailyStats && dailyStats.length > 0 ? (
                dailyStats.map((day, index) => (
                  <div key={index} className="flex flex-1 flex-col justify-end items-center">
                    <div
                      className="w-full bg-orange-500 rounded-t-md transition-all duration-300 ease-in-out"
                      style={{
                        height: `${Math.max(5, calculateBarHeight(day.orderCount || 0))}%`,
                        minWidth: '20px'
                      }}
                    ></div>
                    <div className="pt-2 text-xs text-gray-500 truncate w-full text-center">
                      {formatChartDate(day.date, period)}
                    </div>
                  </div>
                ))
              ) : (
                <div className="w-full flex items-center justify-center text-gray-500">
                  No data available for selected period
                </div>
              )}
            </div>
          )}
        </div> */}
            {/* Quick Actions */}
      <div className="flex flex-col gap-4  ">
        <a
          href="/admin/orders?status=placed"
          className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 hover:shadow-md transition-shadow flex items-center"
        >
          <span className="material-icons-round text-orange-500 bg-orange-50 p-2 rounded-lg mr-3">pending</span>
          <div>
            <p className="font-medium text-gray-800">Pending Orders</p>
            <p className="text-sm text-gray-500">Process {stats.pendingOrders} pending orders</p>
          </div>
        </a>
        <a
          href="/admin/products/new"
          className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 hover:shadow-md transition-shadow flex items-center"
        >
          <span className="material-icons-round text-blue-500 bg-blue-50 p-2 rounded-lg mr-3">add_circle</span>
          <div>
            <p className="font-medium text-gray-800">Add Study Material</p>
            <p className="text-sm text-gray-500">Add new educational content</p>
          </div>
        </a>
        <a
          href="/admin/customers"
          className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 hover:shadow-md transition-shadow flex items-center"
        >
          <span className="material-icons-round text-green-500 bg-green-50 p-2 rounded-lg mr-3">people</span>
          <div>
            <p className="font-medium text-gray-800">Customers</p>
            <p className="text-sm text-gray-500">View customer details</p>
          </div>
        </a>

      </div>

        {/* Stock Requirements Chart */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-medium text-gray-800">Pending Orders Stock Requirements</h3>
            <div>
              <select
                value={period}
                onChange={handlePeriodChange}
                className="border-gray-300 rounded-md text-sm focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="day">Last 24 Hours</option>
                <option value="week">Last 7 Days</option>
                <option value="month">Last 30 Days</option>
                <option value="year">Last Year</option>
              </select>
            </div>
          </div>

          {isLoading ? (
            <div className="h-64 flex items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center">
              {stockRequirements.length > 0 ? (
                <div className="w-full">
                  {/* Simple bar chart for stock requirements */}
                  {stockRequirements.slice(0, 5).map((product, index) => (
                    <div key={index} className="mb-4">
                      <div className="flex justify-between items-center mb-1">
                        <div className="flex items-center">
                          <span className="text-xl mr-2">{product.image}</span>
                          <div>
                            <span className="font-medium">{product.name}</span>
                            {product.unitType !== 'quantity' && (
                              <span className="text-xs text-gray-500 ml-2">
                                {product.unitType === 'ml' || product.unitType === 'l' ? 'Volume' : 'Weight'}
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <span className={`text-sm ${parseInt(product.stockRequired) > 0 ? 'text-orange-600' : 'text-gray-500'}`}>
                            {product.stockRequired > 0 ? `${product.stockRequired} needed` : 'In stock'}
                          </span>
                          {product.stockRequired > 0 && product.unitType !== 'quantity' && (
                            <div className="text-xs text-orange-600">
                              {formatUnitQuantity(product.stockRequired * product.unitValue, product.unitType)}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className={`h-2.5 rounded-full ${product.stockStatus === 'Out of Stock' ? 'bg-red-500' : product.stockStatus === 'Low Stock' ? 'bg-amber-500' : 'bg-green-500'}`}
                          style={{ width: `${Math.min(100, (product.stockRequired / (stockRequirements[0].stockRequired || 1) * 100)) || 0}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-gray-500">No stock requirements data available</div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Recent Orders */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-800">Recent Orders</h3>
          <a href="/admin/orders" className="text-orange-600 hover:text-orange-800 text-sm font-medium flex items-center">
            View All
            <span className="material-icons-round ml-1 text-sm">arrow_forward</span>
          </a>
        </div>
        <DataTable
          columns={orderColumns}
          data={recentOrders}
          isLoading={isLoading}
          pagination={false}
          emptyMessage="No recent orders found"
        />
      </div>

      {/* Stock Requirements for Pending Orders */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-800">Stock Requirements for Pending Orders</h3>
          <div className="flex space-x-2">
            {/* <select
              value={stockFilter}
              onChange={handleStockFilterChange}
              className="border-gray-300 rounded-md text-sm focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="all">All Stock</option>
              <option value="low">Low Stock</option>
              <option value="out">Out of Stock</option>
            </select> */}

            {/* <select
              value={category}
              onChange={handleCategoryChange}
              className="border-gray-300 rounded-md text-sm focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="">All Categories</option>
              {categories.map(cat => (
                <option key={cat.id} value={cat.name}>{cat.name}</option>
              ))}
            </select> */}

            <select
              value={period}
              onChange={handlePeriodChange}
              className="border-gray-300 rounded-md text-sm focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="day">Last 24 Hours</option>
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
              <option value="year">Last Year</option>
            </select>
          </div>
        </div>
        <DataTable
          columns={stockRequirementsColumns}
          data={stockRequirements}
          isLoading={isLoading}
          pagination={false}
          emptyMessage="No stock requirements found"
        />
        <div className="mt-2 flex justify-end">
          <a href="/admin/products" className="text-orange-600 hover:text-orange-800 text-sm font-medium flex items-center">
            Manage Inventory
            <span className="material-icons-round ml-1 text-sm">arrow_forward</span>
          </a>
        </div>
      </div>


    </div>
  );
}