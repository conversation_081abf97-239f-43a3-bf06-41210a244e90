import type { APIRoute } from 'astro';
import { getPaymentMethodSettings, updatePaymentMethodSettings } from '../../../db/database';
import { authMiddleware } from '../../../middleware/auth';

export const prerender = false;

/**
 * Get payment method settings (admin only)
 */
export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Authenticate the user
    const authResult = await authMiddleware({ request }, ['admin']);
    if (authResult instanceof Response) {
      return authResult; // Return authentication error
    }
    
    const settings = await getPaymentMethodSettings(locals.runtime.env);
    
    return new Response(JSON.stringify({
      success: true,
      settings
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error getting payment method settings:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to get payment method settings'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

/**
 * Update payment method settings (admin only)
 */
export const PUT: APIRoute = async ({ request, locals }) => {
  try {
    // Authenticate the user
    const authResult = await authMiddleware({ request }, ['admin']);
    if (authResult instanceof Response) {
      return authResult; // Return authentication error
    }
    
    // Parse request body
    const requestData = await request.json();
    
    // Validate request data
    if (typeof requestData.online_payment_enabled !== 'boolean' || 
        typeof requestData.cash_on_delivery_enabled !== 'boolean') {
      return new Response(JSON.stringify({
        success: false,
        message: 'Invalid payment method settings'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Update settings
    const settings = await updatePaymentMethodSettings(locals.runtime.env, {
      online_payment_enabled: requestData.online_payment_enabled,
      cash_on_delivery_enabled: requestData.cash_on_delivery_enabled
    });
    
    if (!settings) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Failed to update payment method settings'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    return new Response(JSON.stringify({
      success: true,
      settings,
      message: 'Payment method settings updated successfully'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error updating payment method settings:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to update payment method settings'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
